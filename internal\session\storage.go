package session

import (
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// Storage handles persistent storage of sessions
type Storage struct {
	sessionDir string
}

// NewStorage creates a new storage instance
func NewStorage(sessionDir string) (*Storage, error) {
	// Expand home directory if needed
	if strings.HasPrefix(sessionDir, "~/") {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get user home directory: %w", err)
		}
		sessionDir = filepath.Join(homeDir, sessionDir[2:])
	}

	// Create session directory if it doesn't exist
	if err := os.MkdirAll(sessionDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create session directory: %w", err)
	}

	return &Storage{
		sessionDir: sessionDir,
	}, nil
}

// SaveSession saves a session to disk
func (s *Storage) SaveSession(session *types.Session) error {
	if session.ID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	// Update last modified time
	session.LastModified = time.Now()

	// Create session file path
	filename := fmt.Sprintf("%s.json", session.ID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Marshal session to JSON
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Write to file
	if err := os.WriteFile(filepath, data, 0644); err != nil {
		return fmt.Errorf("failed to write session file: %w", err)
	}

	return nil
}

// LoadSession loads a session from disk
func (s *Storage) LoadSession(sessionID string) (*types.Session, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session ID cannot be empty")
	}

	// Create session file path
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return nil, fmt.Errorf("session not found: %s", sessionID)
	}

	// Read file
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read session file: %w", err)
	}

	// Unmarshal session
	var session types.Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	return &session, nil
}

// DeleteSession deletes a session from disk
func (s *Storage) DeleteSession(sessionID string) error {
	if sessionID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	// Create session file path
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	// Delete file
	if err := os.Remove(filepath); err != nil {
		return fmt.Errorf("failed to delete session file: %w", err)
	}

	return nil
}

// ListSessions returns a list of all saved sessions
func (s *Storage) ListSessions() ([]*types.SessionInfo, error) {
	// Read session directory
	entries, err := os.ReadDir(s.sessionDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read session directory: %w", err)
	}

	var sessions []*types.SessionInfo

	for _, entry := range entries {
		if entry.IsDir() || !strings.HasSuffix(entry.Name(), ".json") {
			continue
		}

		// Extract session ID from filename
		sessionID := strings.TrimSuffix(entry.Name(), ".json")

		// Get file info
		info, err := entry.Info()
		if err != nil {
			continue // Skip files we can't read
		}

		// Try to load session metadata
		session, err := s.LoadSession(sessionID)
		if err != nil {
			// If we can't load the session, create basic info from file
			sessions = append(sessions, &types.SessionInfo{
				ID:           sessionID,
				Title:        sessionID,
				CreatedAt:    info.ModTime(),
				LastModified: info.ModTime(),
				MessageCount: 0,
				Size:         info.Size(),
			})
			continue
		}

		// Create session info from loaded session
		sessionInfo := &types.SessionInfo{
			ID:           session.ID,
			Title:        session.Title,
			CreatedAt:    session.CreatedAt,
			LastModified: session.LastModified,
			MessageCount: len(session.Messages),
			TokenUsage:   session.TokenUsage,
			Size:         info.Size(),
		}

		sessions = append(sessions, sessionInfo)
	}

	// Sort sessions by last modified time (newest first)
	sort.Slice(sessions, func(i, j int) bool {
		return sessions[i].LastModified.After(sessions[j].LastModified)
	})

	return sessions, nil
}

// SessionExists checks if a session exists
func (s *Storage) SessionExists(sessionID string) bool {
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)
	_, err := os.Stat(filepath)
	return err == nil
}

// GetSessionSize returns the size of a session file in bytes
func (s *Storage) GetSessionSize(sessionID string) (int64, error) {
	filename := fmt.Sprintf("%s.json", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)
	
	info, err := os.Stat(filepath)
	if err != nil {
		return 0, fmt.Errorf("failed to get session file info: %w", err)
	}
	
	return info.Size(), nil
}

// CleanupOldSessions removes sessions older than the specified duration
func (s *Storage) CleanupOldSessions(maxAge time.Duration) error {
	sessions, err := s.ListSessions()
	if err != nil {
		return fmt.Errorf("failed to list sessions: %w", err)
	}

	cutoff := time.Now().Add(-maxAge)
	var deletedCount int

	for _, session := range sessions {
		if session.LastModified.Before(cutoff) {
			if err := s.DeleteSession(session.ID); err != nil {
				// Log error but continue cleanup
				continue
			}
			deletedCount++
		}
	}

	return nil
}

// GetStorageStats returns statistics about session storage
func (s *Storage) GetStorageStats() (*types.StorageStats, error) {
	sessions, err := s.ListSessions()
	if err != nil {
		return nil, fmt.Errorf("failed to list sessions: %w", err)
	}

	stats := &types.StorageStats{
		TotalSessions: len(sessions),
		TotalSize:     0,
		OldestSession: time.Now(),
		NewestSession: time.Time{},
	}

	for _, session := range sessions {
		stats.TotalSize += session.Size
		
		if session.CreatedAt.Before(stats.OldestSession) {
			stats.OldestSession = session.CreatedAt
		}
		
		if session.CreatedAt.After(stats.NewestSession) {
			stats.NewestSession = session.CreatedAt
		}
	}

	// Calculate directory size
	err = filepath.WalkDir(s.sessionDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if !d.IsDir() {
			info, err := d.Info()
			if err != nil {
				return err
			}
			stats.DirectorySize += info.Size()
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to calculate directory size: %w", err)
	}

	return stats, nil
}

// ExportSession exports a session to a specified file path
func (s *Storage) ExportSession(sessionID, exportPath string) error {
	session, err := s.LoadSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to load session: %w", err)
	}

	// Marshal session to JSON with indentation
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal session: %w", err)
	}

	// Write to export file
	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}

	return nil
}

// ImportSession imports a session from a specified file path
func (s *Storage) ImportSession(importPath string) (*types.Session, error) {
	// Read import file
	data, err := os.ReadFile(importPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read import file: %w", err)
	}

	// Unmarshal session
	var session types.Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("failed to unmarshal session: %w", err)
	}

	// Generate new ID if session already exists
	originalID := session.ID
	counter := 1
	for s.SessionExists(session.ID) {
		session.ID = fmt.Sprintf("%s_%d", originalID, counter)
		counter++
	}

	// Save imported session
	if err := s.SaveSession(&session); err != nil {
		return nil, fmt.Errorf("failed to save imported session: %w", err)
	}

	return &session, nil
}

// SaveMessageHistory saves message history with compression
func (s *Storage) SaveMessageHistory(sessionID string, messages []types.Message) error {
	if sessionID == "" {
		return fmt.Errorf("session ID cannot be empty")
	}

	// Create history file path
	filename := fmt.Sprintf("%s_history.json.gz", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Create file
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create history file: %w", err)
	}
	defer file.Close()

	// Create gzip writer
	gzWriter := gzip.NewWriter(file)
	defer gzWriter.Close()

	// Marshal messages to JSON
	data, err := json.MarshalIndent(messages, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal messages: %w", err)
	}

	// Write compressed data
	if _, err := gzWriter.Write(data); err != nil {
		return fmt.Errorf("failed to write compressed history: %w", err)
	}

	return nil
}

// LoadMessageHistory loads message history with decompression
func (s *Storage) LoadMessageHistory(sessionID string) ([]types.Message, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session ID cannot be empty")
	}

	// Create history file path
	filename := fmt.Sprintf("%s_history.json.gz", sessionID)
	filepath := filepath.Join(s.sessionDir, filename)

	// Check if file exists
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		return []types.Message{}, nil // Return empty slice if no history exists
	}

	// Open file
	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to open history file: %w", err)
	}
	defer file.Close()

	// Create gzip reader
	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer gzReader.Close()

	// Read decompressed data
	data, err := io.ReadAll(gzReader)
	if err != nil {
		return nil, fmt.Errorf("failed to read compressed history: %w", err)
	}

	// Unmarshal messages
	var messages []types.Message
	if err := json.Unmarshal(data, &messages); err != nil {
		return nil, fmt.Errorf("failed to unmarshal messages: %w", err)
	}

	return messages, nil
}

// AppendMessage appends a single message to the history
func (s *Storage) AppendMessage(sessionID string, message types.Message) error {
	// Load existing messages
	messages, err := s.LoadMessageHistory(sessionID)
	if err != nil {
		return fmt.Errorf("failed to load existing history: %w", err)
	}

	// Append new message
	messages = append(messages, message)

	// Save updated history
	return s.SaveMessageHistory(sessionID, messages)
}

// GetMessageCount returns the number of messages in a session
func (s *Storage) GetMessageCount(sessionID string) (int, error) {
	messages, err := s.LoadMessageHistory(sessionID)
	if err != nil {
		return 0, err
	}
	return len(messages), nil
}

// GetRecentMessages returns the most recent N messages from a session
func (s *Storage) GetRecentMessages(sessionID string, count int) ([]types.Message, error) {
	messages, err := s.LoadMessageHistory(sessionID)
	if err != nil {
		return nil, err
	}

	if len(messages) <= count {
		return messages, nil
	}

	return messages[len(messages)-count:], nil
}

// SearchMessages searches for messages containing specific text
func (s *Storage) SearchMessages(sessionID, query string) ([]types.Message, error) {
	messages, err := s.LoadMessageHistory(sessionID)
	if err != nil {
		return nil, err
	}

	var results []types.Message
	query = strings.ToLower(query)

	for _, message := range messages {
		if strings.Contains(strings.ToLower(message.Content), query) {
			results = append(results, message)
		}
	}

	return results, nil
}

// ExportMessages exports messages to a specific format
func (s *Storage) ExportMessages(sessionID, format string) ([]byte, error) {
	messages, err := s.LoadMessageHistory(sessionID)
	if err != nil {
		return nil, err
	}

	switch strings.ToLower(format) {
	case "json":
		return json.MarshalIndent(messages, "", "  ")
	case "text":
		return s.exportAsText(messages), nil
	case "markdown":
		return s.exportAsMarkdown(messages), nil
	default:
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}
}

// exportAsText exports messages as plain text
func (s *Storage) exportAsText(messages []types.Message) []byte {
	var result strings.Builder

	for _, message := range messages {
		timestamp := message.Timestamp.Format("2006-01-02 15:04:05")
		result.WriteString(fmt.Sprintf("[%s] %s: %s\n\n", timestamp, message.Role, message.Content))
	}

	return []byte(result.String())
}

// exportAsMarkdown exports messages as markdown
func (s *Storage) exportAsMarkdown(messages []types.Message) []byte {
	var result strings.Builder

	result.WriteString("# Chat Session Export\n\n")

	for _, message := range messages {
		timestamp := message.Timestamp.Format("2006-01-02 15:04:05")

		switch message.Role {
		case types.RoleUser:
			result.WriteString(fmt.Sprintf("## User (%s)\n\n%s\n\n", timestamp, message.Content))
		case types.RoleAssistant:
			result.WriteString(fmt.Sprintf("## Assistant (%s)\n\n%s\n\n", timestamp, message.Content))
		case types.RoleSystem:
			result.WriteString(fmt.Sprintf("### System (%s)\n\n%s\n\n", timestamp, message.Content))
		default:
			result.WriteString(fmt.Sprintf("### %s (%s)\n\n%s\n\n", message.Role, timestamp, message.Content))
		}
	}

	return []byte(result.String())
}
