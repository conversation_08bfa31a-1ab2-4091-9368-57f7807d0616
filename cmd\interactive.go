package cmd

import (
	"fmt"
	"os"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/spf13/cobra"

	"arien-ai/internal/ui/models"
)

// interactiveCmd represents the interactive command
var interactiveCmd = &cobra.Command{
	Use:   "interactive",
	Short: "Start interactive terminal mode",
	Long: `Start the interactive terminal mode where you can chat with the AI
and execute commands. This is the main interface for Arien-AI.

Features:
- Real-time chat with LLM
- Command execution with safety checks
- Session management
- Slash commands for quick actions
- Message history and context`,
	Aliases: []string{"i", "chat", "terminal"},
	Run: func(cmd *cobra.Command, args []string) {
		runInteractiveCommand(cmd, args)
	},
}

func init() {
	interactiveCmd.Flags().String("session", "", "Load specific session by ID")
	interactiveCmd.Flags().String("provider", "", "Override LLM provider for this session")
	interactiveCmd.Flags().String("model", "", "Override model for this session")
	interactiveCmd.Flags().Bool("safe-mode", false, "Enable safe mode (extra safety checks)")
	interactiveCmd.Flags().Bool("no-history", false, "Don't save conversation history")
	interactiveCmd.Flags().Bool("debug", false, "Enable debug mode")
}

func runInteractiveCommand(cmd *cobra.Command, args []string) {
	// Get command line options
	sessionID, _ := cmd.Flags().GetString("session")
	providerOverride, _ := cmd.Flags().GetString("provider")
	modelOverride, _ := cmd.Flags().GetString("model")
	safeMode, _ := cmd.Flags().GetBool("safe-mode")
	noHistory, _ := cmd.Flags().GetBool("no-history")
	debugMode, _ := cmd.Flags().GetBool("debug")

	runInteractiveWithOptions(sessionID, providerOverride, modelOverride, safeMode, noHistory, debugMode)
}

func runInteractiveWithOptions(sessionID, providerOverride, modelOverride string, safeMode, noHistory, debugMode bool) {
	// Check if configuration exists
	if configManager.IsFirstRun() {
		fmt.Println("⚠️  Arien-AI is not configured yet.")
		fmt.Println("Please run 'arien-ai onboard' first to set up your configuration.")
		return
	}

	// Validate configuration
	if err := configManager.Validate(); err != nil {
		fmt.Fprintf(os.Stderr, "❌ Configuration validation failed: %v\n", err)
		fmt.Println("Please run 'arien-ai config show' to check your configuration.")
		fmt.Println("Or run 'arien-ai onboard --force' to reconfigure.")
		return
	}

	// Options are passed as parameters

	// Create interactive model options
	options := models.InteractiveOptions{
		SessionID:        sessionID,
		ProviderOverride: providerOverride,
		ModelOverride:    modelOverride,
		SafeMode:         safeMode,
		NoHistory:        noHistory,
		DebugMode:        debugMode,
	}

	// Create main terminal model
	model, err := models.NewMainModel(configManager, options)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating terminal model: %v\n", err)
		os.Exit(1)
	}

	// Create and run the Bubble Tea program
	p := tea.NewProgram(
		model,
		tea.WithAltScreen(),
		tea.WithMouseCellMotion(),
	)

	// Handle graceful shutdown
	defer func() {
		if r := recover(); r != nil {
			fmt.Fprintf(os.Stderr, "Panic in interactive mode: %v\n", r)
		}
	}()

	// Run the program
	finalModel, err := p.Run()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error running interactive mode: %v\n", err)
		os.Exit(1)
	}

	// Handle cleanup and final messages
	if mainModel, ok := finalModel.(*models.MainModel); ok {
		handleInteractiveExit(mainModel)
	}
}

func handleInteractiveExit(model *models.MainModel) {
	// Get exit information from the model
	exitInfo := model.GetExitInfo()
	
	// Display exit message
	if exitInfo.Message != "" {
		fmt.Println(exitInfo.Message)
	}
	
	// Save session if needed
	if exitInfo.ShouldSaveSession {
		fmt.Println("💾 Saving session...")
		if err := model.SaveCurrentSession(); err != nil {
			fmt.Fprintf(os.Stderr, "⚠️  Failed to save session: %v\n", err)
		} else {
			fmt.Println("✅ Session saved successfully")
		}
	}
	
	// Display session statistics
	if stats := exitInfo.SessionStats; stats != nil {
		fmt.Printf("\n📊 Session Statistics:\n")
		fmt.Printf("  Messages: %d\n", stats.MessageCount)
		fmt.Printf("  Commands executed: %d\n", stats.CommandCount)
		fmt.Printf("  Duration: %v\n", stats.Duration)
		if stats.TokenUsage.TotalTokens > 0 {
			fmt.Printf("  Tokens used: %d\n", stats.TokenUsage.TotalTokens)
		}
	}
	
	// Display helpful exit messages
	fmt.Println("\n👋 Thanks for using Arien-AI!")
	fmt.Println("Run 'arien-ai interactive' to start a new session.")
	
	if exitInfo.HasErrors {
		fmt.Println("⚠️  Some errors occurred during this session.")
		fmt.Println("Check the logs or run with --debug for more information.")
	}
}

// Helper function to check if interactive mode can start
func canStartInteractive() error {
	// Check configuration
	if configManager.IsFirstRun() {
		return fmt.Errorf("not configured - run 'arien-ai onboard' first")
	}
	
	// Validate configuration
	if err := configManager.Validate(); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}
	
	// Check if session directory exists
	if err := configManager.EnsureSessionDirectory(); err != nil {
		return fmt.Errorf("failed to create session directory: %w", err)
	}
	
	return nil
}

// Helper function to display pre-start information
func displayStartupInfo() {
	config := configManager.Get()
	
	fmt.Printf("🚀 Starting Arien-AI Interactive Mode\n")
	fmt.Printf("Provider: %s\n", config.LLM.Provider)
	fmt.Printf("Model: %s\n", config.LLM.Model)
	
	if config.Security.SandboxMode {
		fmt.Printf("🔒 Sandbox mode: ENABLED\n")
	}
	
	fmt.Printf("\nPress Ctrl+C or type '/quit' to exit\n")
	fmt.Printf("Type '/' for slash commands\n")
	fmt.Printf("─────────────────────────────────────\n\n")
}

// Helper function to handle startup errors
func handleStartupError(err error) {
	fmt.Fprintf(os.Stderr, "❌ Failed to start interactive mode: %v\n", err)
	
	// Provide helpful suggestions based on error type
	errorMsg := err.Error()
	
	if contains(errorMsg, "not configured") {
		fmt.Println("\n💡 Suggestion: Run 'arien-ai onboard' to set up your configuration")
	} else if contains(errorMsg, "invalid configuration") {
		fmt.Println("\n💡 Suggestions:")
		fmt.Println("  - Run 'arien-ai config show' to check your configuration")
		fmt.Println("  - Run 'arien-ai onboard --force' to reconfigure")
	} else if contains(errorMsg, "API key") {
		fmt.Println("\n💡 Suggestion: Set your API key with 'arien-ai config set-api-key <key>'")
	} else if contains(errorMsg, "connection") {
		fmt.Println("\n💡 Suggestions:")
		fmt.Println("  - Check your internet connection")
		fmt.Println("  - Verify your API key is correct")
		fmt.Println("  - For Ollama: ensure Ollama is running locally")
	}
	
	os.Exit(1)
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
			 s[len(s)-len(substr):] == substr ||
			 containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
