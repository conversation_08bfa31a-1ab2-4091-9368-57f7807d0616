package styles

import (
	"github.com/charmbracelet/lipgloss"
)

// Color palette
var (
	// Primary colors
	PrimaryColor   = lipgloss.Color("#00D4AA")
	SecondaryColor = lipgloss.Color("#FF6B6B")
	AccentColor    = lipgloss.Color("#4ECDC4")
	
	// Text colors
	TextPrimary   = lipgloss.Color("#FFFFFF")
	TextSecondary = lipgloss.Color("#B0B0B0")
	TextMuted     = lipgloss.Color("#666666")
	TextError     = lipgloss.Color("#FF6B6B")
	TextSuccess   = lipgloss.Color("#4ECDC4")
	TextWarning   = lipgloss.Color("#FFE66D")
	
	// Background colors
	BackgroundPrimary   = lipgloss.Color("#1A1A1A")
	BackgroundSecondary = lipgloss.Color("#2D2D2D")
	BackgroundTertiary  = lipgloss.Color("#3D3D3D")
	
	// Border colors
	BorderPrimary   = lipgloss.Color("#444444")
	BorderSecondary = lipgloss.Color("#666666")
	BorderActive    = lipgloss.Color("#00D4AA")
)

// Theme represents a UI theme
type Theme struct {
	Name        string
	Colors      ColorScheme
	Styles      StyleSet
	BorderColor lipgloss.Color
	AccentColor lipgloss.Color
}

// ColorScheme defines the color scheme for a theme
type ColorScheme struct {
	Primary     lipgloss.Color
	Secondary   lipgloss.Color
	Accent      lipgloss.Color
	Background  lipgloss.Color
	Surface     lipgloss.Color
	Text        lipgloss.Color
	TextMuted   lipgloss.Color
	Border      lipgloss.Color
	BorderActive lipgloss.Color
	Success     lipgloss.Color
	Warning     lipgloss.Color
	Error       lipgloss.Color
}

// StyleSet contains all the styles for the UI
type StyleSet struct {
	// Container styles
	AppContainer    lipgloss.Style
	HeaderContainer lipgloss.Style
	ChatContainer   lipgloss.Style
	InputContainer  lipgloss.Style
	StatusContainer lipgloss.Style
	
	// Text styles
	HeaderText       lipgloss.Style
	UserMessage      lipgloss.Style
	AssistantMessage lipgloss.Style
	SystemMessage    lipgloss.Style
	ErrorMessage     lipgloss.Style
	SuccessMessage   lipgloss.Style
	WarningMessage   lipgloss.Style
	TextPrimary      lipgloss.Style
	TextSecondary    lipgloss.Style
	TextMuted        lipgloss.Style
	TextWarning      lipgloss.Style
	
	// Input styles
	InputField      lipgloss.Style
	InputPrompt     lipgloss.Style
	InputFocused    lipgloss.Style
	
	// Component styles
	Button          lipgloss.Style
	ButtonActive    lipgloss.Style
	ButtonDisabled  lipgloss.Style
	
	// Animation styles
	ThinkingDots    lipgloss.Style
	LoadingSpinner  lipgloss.Style
	
	// Command styles
	CommandText     lipgloss.Style
	CommandOutput   lipgloss.Style
	CommandError    lipgloss.Style
	
	// Slash command styles
	SlashMenu       lipgloss.Style
	SlashMenuItem   lipgloss.Style
	SlashMenuActive lipgloss.Style
}

// DefaultTheme returns the default theme
func DefaultTheme() *Theme {
	colors := ColorScheme{
		Primary:      PrimaryColor,
		Secondary:    SecondaryColor,
		Accent:       AccentColor,
		Background:   BackgroundPrimary,
		Surface:      BackgroundSecondary,
		Text:         TextPrimary,
		TextMuted:    TextSecondary,
		Border:       BorderPrimary,
		BorderActive: BorderActive,
		Success:      TextSuccess,
		Warning:      TextWarning,
		Error:        TextError,
	}
	
	return &Theme{
		Name:        "default",
		Colors:      colors,
		Styles:      createStyleSet(colors),
		BorderColor: colors.Border,
		AccentColor: colors.Primary,
	}
}

// DarkTheme returns a dark theme variant
func DarkTheme() *Theme {
	colors := ColorScheme{
		Primary:      lipgloss.Color("#BB86FC"),
		Secondary:    lipgloss.Color("#03DAC6"),
		Accent:       lipgloss.Color("#CF6679"),
		Background:   lipgloss.Color("#121212"),
		Surface:      lipgloss.Color("#1E1E1E"),
		Text:         lipgloss.Color("#FFFFFF"),
		TextMuted:    lipgloss.Color("#AAAAAA"),
		Border:       lipgloss.Color("#333333"),
		BorderActive: lipgloss.Color("#BB86FC"),
		Success:      lipgloss.Color("#4CAF50"),
		Warning:      lipgloss.Color("#FF9800"),
		Error:        lipgloss.Color("#F44336"),
	}
	
	return &Theme{
		Name:        "dark",
		Colors:      colors,
		Styles:      createStyleSet(colors),
		BorderColor: colors.Border,
		AccentColor: colors.Primary,
	}
}

// LightTheme returns a light theme variant
func LightTheme() *Theme {
	colors := ColorScheme{
		Primary:      lipgloss.Color("#6200EE"),
		Secondary:    lipgloss.Color("#018786"),
		Accent:       lipgloss.Color("#B00020"),
		Background:   lipgloss.Color("#FFFFFF"),
		Surface:      lipgloss.Color("#F5F5F5"),
		Text:         lipgloss.Color("#000000"),
		TextMuted:    lipgloss.Color("#666666"),
		Border:       lipgloss.Color("#E0E0E0"),
		BorderActive: lipgloss.Color("#6200EE"),
		Success:      lipgloss.Color("#4CAF50"),
		Warning:      lipgloss.Color("#FF9800"),
		Error:        lipgloss.Color("#F44336"),
	}
	
	return &Theme{
		Name:        "light",
		Colors:      colors,
		Styles:      createStyleSet(colors),
		BorderColor: colors.Border,
		AccentColor: colors.Primary,
	}
}

// createStyleSet creates a complete style set from a color scheme
func createStyleSet(colors ColorScheme) StyleSet {
	return StyleSet{
		// Container styles
		AppContainer: lipgloss.NewStyle().
			Background(colors.Background).
			Foreground(colors.Text).
			Padding(0).
			Margin(0),
			
		HeaderContainer: lipgloss.NewStyle().
			Background(colors.Surface).
			Foreground(colors.Text).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.Border).
			BorderBottom(true).
			Padding(0, 1).
			Width(100),
			
		ChatContainer: lipgloss.NewStyle().
			Background(colors.Background).
			Foreground(colors.Text).
			Padding(1).
			Height(20),
			
		InputContainer: lipgloss.NewStyle().
			Background(colors.Surface).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.Border).
			BorderTop(true).
			Padding(0, 1),
			
		StatusContainer: lipgloss.NewStyle().
			Background(colors.Surface).
			Foreground(colors.TextMuted).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.Border).
			BorderTop(true).
			Padding(0, 1).
			Height(1),
		
		// Text styles
		HeaderText: lipgloss.NewStyle().
			Foreground(colors.Primary).
			Bold(true),
			
		UserMessage: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Primary).
			Padding(0, 1).
			MarginRight(10).
			Align(lipgloss.Right),
			
		AssistantMessage: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Surface).
			Padding(0, 1).
			MarginLeft(2),
			
		SystemMessage: lipgloss.NewStyle().
			Foreground(colors.TextMuted).
			Italic(true).
			MarginLeft(2),
			
		ErrorMessage: lipgloss.NewStyle().
			Foreground(colors.Error).
			Bold(true),
			
		SuccessMessage: lipgloss.NewStyle().
			Foreground(colors.Success).
			Bold(true),
			
		WarningMessage: lipgloss.NewStyle().
			Foreground(colors.Warning).
			Bold(true),

		TextPrimary: lipgloss.NewStyle().
			Foreground(colors.Text),

		TextSecondary: lipgloss.NewStyle().
			Foreground(colors.TextMuted),

		TextMuted: lipgloss.NewStyle().
			Foreground(colors.TextMuted),

		TextWarning: lipgloss.NewStyle().
			Foreground(colors.Warning),
		
		// Input styles
		InputField: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Background).
			Padding(0, 1),
			
		InputPrompt: lipgloss.NewStyle().
			Foreground(colors.Primary).
			Bold(true),
			
		InputFocused: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Background).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.BorderActive).
			Padding(0, 1),
		
		// Component styles
		Button: lipgloss.NewStyle().
			Foreground(colors.Text).
			Background(colors.Surface).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.Border).
			Padding(0, 2).
			MarginRight(1),
			
		ButtonActive: lipgloss.NewStyle().
			Foreground(colors.Background).
			Background(colors.Primary).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.Primary).
			Padding(0, 2).
			MarginRight(1).
			Bold(true),
			
		ButtonDisabled: lipgloss.NewStyle().
			Foreground(colors.TextMuted).
			Background(colors.Surface).
			Border(lipgloss.NormalBorder()).
			BorderForeground(colors.Border).
			Padding(0, 2).
			MarginRight(1),
		
		// Animation styles
		ThinkingDots: lipgloss.NewStyle().
			Foreground(colors.Primary).
			Bold(true),
			
		LoadingSpinner: lipgloss.NewStyle().
			Foreground(colors.Accent).
			Bold(true),
		
		// Command styles
		CommandText: lipgloss.NewStyle().
			Foreground(colors.Accent).
			Background(colors.Surface).
			Padding(0, 1).
			MarginLeft(2),
			
		CommandOutput: lipgloss.NewStyle().
			Foreground(colors.TextMuted).
			Background(colors.Background).
			Padding(0, 1).
			MarginLeft(4),
			
		CommandError: lipgloss.NewStyle().
			Foreground(colors.Error).
			Background(colors.Background).
			Padding(0, 1).
			MarginLeft(4),
		
		// Slash command styles
		SlashMenu: lipgloss.NewStyle().
			Background(colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(colors.Border).
			Padding(1).
			MarginTop(1),
			
		SlashMenuItem: lipgloss.NewStyle().
			Foreground(colors.Text).
			Padding(0, 1),
			
		SlashMenuActive: lipgloss.NewStyle().
			Foreground(colors.Background).
			Background(colors.Primary).
			Padding(0, 1).
			Bold(true),
	}
}

// GetTheme returns a theme by name
func GetTheme(name string) *Theme {
	switch name {
	case "dark":
		return DarkTheme()
	case "light":
		return LightTheme()
	default:
		return DefaultTheme()
	}
}

// GetAvailableThemes returns a list of available theme names
func GetAvailableThemes() []string {
	return []string{"default", "dark", "light"}
}

// NewTheme creates a new theme by name
func NewTheme(name string) *Theme {
	return GetTheme(name)
}
