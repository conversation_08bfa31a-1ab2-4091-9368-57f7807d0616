package models

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/config"
	"arien-ai/internal/llm"
	"arien-ai/internal/ui/styles"
	"arien-ai/pkg/types"
)

// OnboardingStep represents the current step in onboarding
type OnboardingStep int

const (
	StepWelcome OnboardingStep = iota
	StepProvider
	StepModel
	StepAPIKey
	StepBaseURL
	StepConfirmation
	StepComplete
)

// OnboardingModel represents the onboarding UI model
type OnboardingModel struct {
	configManager *config.Manager
	theme         *styles.Theme
	
	// Current state
	currentStep OnboardingStep
	completed   bool
	
	// Configuration being built
	provider string
	model    string
	apiKey   string
	baseURL  string
	
	// Pre-filled values
	preProvider string
	preModel    string
	preAPIKey   string
	
	// UI components
	textInput textinput.Model
	
	// Available options
	providers []string
	models    []string
	
	// Selection state
	selectedIndex int
	
	// Error state
	errorMessage string
	
	// Window dimensions
	width  int
	height int
}

// NewOnboardingModel creates a new onboarding model
func NewOnboardingModel(configManager *config.Manager, preProvider, preModel, preAPIKey string) *OnboardingModel {
	ti := textinput.New()
	ti.Placeholder = "Enter value..."
	ti.CharLimit = 256
	ti.Width = 50
	
	return &OnboardingModel{
		configManager: configManager,
		theme:         styles.DefaultTheme(),
		currentStep:   StepWelcome,
		preProvider:   preProvider,
		preModel:      preModel,
		preAPIKey:     preAPIKey,
		textInput:     ti,
		providers:     llm.GetAvailableProviders(),
		selectedIndex: 0,
	}
}

// Init initializes the onboarding model
func (m *OnboardingModel) Init() tea.Cmd {
	return textinput.Blink
}

// Update handles messages and updates the model
func (m *OnboardingModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil
		
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
			
		case "enter":
			return m, m.handleEnter()
			
		case "up", "k":
			if m.currentStep == StepProvider || m.currentStep == StepModel {
				if m.selectedIndex > 0 {
					m.selectedIndex--
				}
			}
			
		case "down", "j":
			if m.currentStep == StepProvider {
				if m.selectedIndex < len(m.providers)-1 {
					m.selectedIndex++
				}
			} else if m.currentStep == StepModel {
				if m.selectedIndex < len(m.models)-1 {
					m.selectedIndex++
				}
			}
			
		case "y", "Y":
			if m.currentStep == StepConfirmation {
				return m, m.saveConfiguration()
			}
			
		case "n", "N":
			if m.currentStep == StepConfirmation {
				m.currentStep = StepProvider
				m.selectedIndex = 0
				m.errorMessage = ""
			}
		}
	}
	
	// Update text input for relevant steps
	if m.currentStep == StepAPIKey || m.currentStep == StepBaseURL {
		m.textInput, cmd = m.textInput.Update(msg)
	}
	
	return m, cmd
}

// View renders the onboarding UI
func (m *OnboardingModel) View() string {
	if m.width == 0 {
		return "Loading..."
	}
	
	var content string
	
	switch m.currentStep {
	case StepWelcome:
		content = m.renderWelcome()
	case StepProvider:
		content = m.renderProviderSelection()
	case StepModel:
		content = m.renderModelSelection()
	case StepAPIKey:
		content = m.renderAPIKeyInput()
	case StepBaseURL:
		content = m.renderBaseURLInput()
	case StepConfirmation:
		content = m.renderConfirmation()
	case StepComplete:
		content = m.renderComplete()
	}
	
	// Add error message if present
	if m.errorMessage != "" {
		errorStyle := m.theme.Styles.ErrorMessage
		content += "\n\n" + errorStyle.Render("❌ "+m.errorMessage)
	}
	
	// Center content
	containerStyle := lipgloss.NewStyle().
		Width(m.width).
		Height(m.height).
		Align(lipgloss.Center, lipgloss.Center)
	
	return containerStyle.Render(content)
}

// renderWelcome renders the welcome step
func (m *OnboardingModel) renderWelcome() string {
	title := m.theme.Styles.HeaderText.Render("🚀 Welcome to Arien-AI!")
	
	description := `A powerful CLI terminal system with LLM integration.

This setup wizard will help you configure:
• LLM Provider (Deepseek or Ollama)
• Model selection
• API credentials
• Connection settings

Press Enter to continue or Ctrl+C to exit`
	
	return lipgloss.JoinVertical(lipgloss.Center, title, "", description)
}

// renderProviderSelection renders the provider selection step
func (m *OnboardingModel) renderProviderSelection() string {
	title := m.theme.Styles.HeaderText.Render("Select LLM Provider")
	
	var options []string
	for i, provider := range m.providers {
		style := m.theme.Styles.SlashMenuItem
		if i == m.selectedIndex {
			style = m.theme.Styles.SlashMenuActive
		}
		
		description := ""
		switch provider {
		case "deepseek":
			description = " - Cloud-based AI with reasoning capabilities"
		case "ollama":
			description = " - Local AI models (requires Ollama installation)"
		}
		
		options = append(options, style.Render(fmt.Sprintf("  %s%s", provider, description)))
	}
	
	instructions := "Use ↑/↓ to navigate, Enter to select"
	
	return lipgloss.JoinVertical(lipgloss.Center,
		title,
		"",
		strings.Join(options, "\n"),
		"",
		instructions,
	)
}

// renderModelSelection renders the model selection step
func (m *OnboardingModel) renderModelSelection() string {
	title := m.theme.Styles.HeaderText.Render(fmt.Sprintf("Select Model for %s", m.provider))
	
	var options []string
	for i, model := range m.models {
		style := m.theme.Styles.SlashMenuItem
		if i == m.selectedIndex {
			style = m.theme.Styles.SlashMenuActive
		}
		
		options = append(options, style.Render(fmt.Sprintf("  %s", model)))
	}
	
	instructions := "Use ↑/↓ to navigate, Enter to select"
	
	return lipgloss.JoinVertical(lipgloss.Center,
		title,
		"",
		strings.Join(options, "\n"),
		"",
		instructions,
	)
}

// renderAPIKeyInput renders the API key input step
func (m *OnboardingModel) renderAPIKeyInput() string {
	title := m.theme.Styles.HeaderText.Render("Enter API Key")
	
	description := fmt.Sprintf("Please enter your %s API key:", m.provider)
	if m.provider == "ollama" {
		description = "Ollama runs locally, no API key needed."
	}
	
	m.textInput.Placeholder = "sk-..."
	m.textInput.EchoMode = textinput.EchoPassword
	m.textInput.Focus()
	
	instructions := "Enter your API key and press Enter"
	if m.provider == "ollama" {
		instructions = "Press Enter to continue (no API key needed)"
	}
	
	return lipgloss.JoinVertical(lipgloss.Center,
		title,
		"",
		description,
		"",
		m.textInput.View(),
		"",
		instructions,
	)
}

// renderBaseURLInput renders the base URL input step
func (m *OnboardingModel) renderBaseURLInput() string {
	title := m.theme.Styles.HeaderText.Render("Base URL (Optional)")
	
	description := "Enter custom base URL or press Enter for default:"
	
	defaultURL := ""
	switch m.provider {
	case "deepseek":
		defaultURL = "https://api.deepseek.com"
	case "ollama":
		defaultURL = "http://localhost:11434/v1"
	}
	
	m.textInput.Placeholder = defaultURL
	m.textInput.EchoMode = textinput.EchoNormal
	m.textInput.Focus()
	
	instructions := "Press Enter to use default or enter custom URL"
	
	return lipgloss.JoinVertical(lipgloss.Center,
		title,
		"",
		description,
		fmt.Sprintf("Default: %s", defaultURL),
		"",
		m.textInput.View(),
		"",
		instructions,
	)
}

// renderConfirmation renders the confirmation step
func (m *OnboardingModel) renderConfirmation() string {
	title := m.theme.Styles.HeaderText.Render("Confirm Configuration")
	
	config := fmt.Sprintf(`Provider: %s
Model: %s
API Key: %s
Base URL: %s`,
		m.provider,
		m.model,
		m.maskAPIKey(m.apiKey),
		m.baseURL,
	)
	
	instructions := "Save this configuration? (y/n)"
	
	return lipgloss.JoinVertical(lipgloss.Center,
		title,
		"",
		config,
		"",
		instructions,
	)
}

// renderComplete renders the completion step
func (m *OnboardingModel) renderComplete() string {
	title := m.theme.Styles.SuccessMessage.Render("✅ Setup Complete!")
	
	message := `Configuration saved successfully!

You can now start using Arien-AI with:
  arien-ai interactive

Press any key to exit`
	
	return lipgloss.JoinVertical(lipgloss.Center, title, "", message)
}

// Helper methods

func (m *OnboardingModel) handleEnter() tea.Cmd {
	switch m.currentStep {
	case StepWelcome:
		m.currentStep = StepProvider
		if m.preProvider != "" {
			for i, p := range m.providers {
				if p == m.preProvider {
					m.selectedIndex = i
					break
				}
			}
		}
		
	case StepProvider:
		m.provider = m.providers[m.selectedIndex]
		m.models = llm.GetDefaultModels()[m.provider]
		m.selectedIndex = 0
		m.currentStep = StepModel
		
		if m.preModel != "" {
			for i, model := range m.models {
				if model == m.preModel {
					m.selectedIndex = i
					break
				}
			}
		}
		
	case StepModel:
		m.model = m.models[m.selectedIndex]
		m.currentStep = StepAPIKey
		
		if m.preAPIKey != "" {
			m.textInput.SetValue(m.preAPIKey)
		}
		
	case StepAPIKey:
		m.apiKey = m.textInput.Value()
		m.textInput.SetValue("")
		m.currentStep = StepBaseURL
		
	case StepBaseURL:
		m.baseURL = m.textInput.Value()
		if m.baseURL == "" {
			switch m.provider {
			case "deepseek":
				m.baseURL = "https://api.deepseek.com"
			case "ollama":
				m.baseURL = "http://localhost:11434/v1"
			}
		}
		m.currentStep = StepConfirmation
		
	case StepComplete:
		return tea.Quit
	}
	
	return nil
}

func (m *OnboardingModel) saveConfiguration() tea.Cmd {
	// Create LLM config
	llmConfig := types.LLMConfig{
		Provider:    m.provider,
		Model:       m.model,
		APIKey:      m.apiKey,
		BaseURL:     m.baseURL,
		Temperature: 0.7,
		MaxTokens:   4096,
	}
	
	// Update configuration
	if err := m.configManager.UpdateLLMConfig(llmConfig); err != nil {
		m.errorMessage = fmt.Sprintf("Failed to save configuration: %v", err)
		return nil
	}
	
	m.completed = true
	m.currentStep = StepComplete
	return nil
}

func (m *OnboardingModel) maskAPIKey(key string) string {
	if key == "" {
		return "(not set)"
	}
	if len(key) <= 8 {
		return strings.Repeat("*", len(key))
	}
	return key[:4] + strings.Repeat("*", len(key)-8) + key[len(key)-4:]
}

// IsCompleted returns whether onboarding was completed successfully
func (m *OnboardingModel) IsCompleted() bool {
	return m.completed
}
