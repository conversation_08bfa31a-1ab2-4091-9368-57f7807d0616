package cache

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"arien-ai/pkg/types"
)

// CacheEntry represents a cached item
type CacheEntry struct {
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	ExpiresAt time.Time   `json:"expires_at"`
	CreatedAt time.Time   `json:"created_at"`
	AccessCount int       `json:"access_count"`
	LastAccess  time.Time `json:"last_access"`
}

// Cache represents the cache system
type Cache struct {
	entries   map[string]*CacheEntry
	mutex     sync.RWMutex
	cacheDir  string
	maxSize   int
	ttl       time.Duration
	cleanupInterval time.Duration
	stopCleanup     chan bool
}

// CacheConfig contains cache configuration
type CacheConfig struct {
	CacheDir        string        `yaml:"cache_dir" json:"cache_dir"`
	MaxSize         int           `yaml:"max_size" json:"max_size"`
	TTL             time.Duration `yaml:"ttl" json:"ttl"`
	CleanupInterval time.Duration `yaml:"cleanup_interval" json:"cleanup_interval"`
	Enabled         bool          `yaml:"enabled" json:"enabled"`
}

// DefaultCacheConfig returns default cache configuration
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		CacheDir:        filepath.Join(os.TempDir(), "arien-ai-cache"),
		MaxSize:         1000,
		TTL:             24 * time.Hour,
		CleanupInterval: 1 * time.Hour,
		Enabled:         true,
	}
}

// NewCache creates a new cache instance
func NewCache(config *CacheConfig) (*Cache, error) {
	if !config.Enabled {
		return &Cache{
			entries: make(map[string]*CacheEntry),
		}, nil
	}

	// Create cache directory if it doesn't exist
	if err := os.MkdirAll(config.CacheDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create cache directory: %w", err)
	}

	cache := &Cache{
		entries:         make(map[string]*CacheEntry),
		cacheDir:        config.CacheDir,
		maxSize:         config.MaxSize,
		ttl:             config.TTL,
		cleanupInterval: config.CleanupInterval,
		stopCleanup:     make(chan bool),
	}

	// Load existing cache entries
	if err := cache.loadFromDisk(); err != nil {
		// Log error but don't fail - cache can work without persistence
		fmt.Printf("Warning: failed to load cache from disk: %v\n", err)
	}

	// Start cleanup goroutine
	go cache.startCleanup()

	return cache, nil
}

// Get retrieves a value from the cache
func (c *Cache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, exists := c.entries[key]
	if !exists {
		return nil, false
	}

	// Check if expired
	if time.Now().After(entry.ExpiresAt) {
		// Remove expired entry
		delete(c.entries, key)
		return nil, false
	}

	// Update access statistics
	entry.AccessCount++
	entry.LastAccess = time.Now()

	return entry.Value, true
}

// Set stores a value in the cache
func (c *Cache) Set(key string, value interface{}) {
	c.SetWithTTL(key, value, c.ttl)
}

// SetWithTTL stores a value in the cache with custom TTL
func (c *Cache) SetWithTTL(key string, value interface{}, ttl time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	entry := &CacheEntry{
		Key:         key,
		Value:       value,
		ExpiresAt:   now.Add(ttl),
		CreatedAt:   now,
		AccessCount: 0,
		LastAccess:  now,
	}

	c.entries[key] = entry

	// Check if we need to evict entries
	if len(c.entries) > c.maxSize {
		c.evictLRU()
	}
}

// Delete removes a value from the cache
func (c *Cache) Delete(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.entries, key)
}

// Clear removes all entries from the cache
func (c *Cache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.entries = make(map[string]*CacheEntry)
}

// Size returns the number of entries in the cache
func (c *Cache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return len(c.entries)
}

// Keys returns all keys in the cache
func (c *Cache) Keys() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	keys := make([]string, 0, len(c.entries))
	for key := range c.entries {
		keys = append(keys, key)
	}
	return keys
}

// Stats returns cache statistics
func (c *Cache) Stats() CacheStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	stats := CacheStats{
		Size:    len(c.entries),
		MaxSize: c.maxSize,
	}

	for _, entry := range c.entries {
		stats.TotalAccesses += entry.AccessCount
		if entry.LastAccess.After(stats.LastAccess) {
			stats.LastAccess = entry.LastAccess
		}
	}

	return stats
}

// CacheStats contains cache statistics
type CacheStats struct {
	Size          int       `json:"size"`
	MaxSize       int       `json:"max_size"`
	TotalAccesses int       `json:"total_accesses"`
	LastAccess    time.Time `json:"last_access"`
}

// evictLRU removes the least recently used entry
func (c *Cache) evictLRU() {
	var oldestKey string
	var oldestTime time.Time

	for key, entry := range c.entries {
		if oldestKey == "" || entry.LastAccess.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.LastAccess
		}
	}

	if oldestKey != "" {
		delete(c.entries, oldestKey)
	}
}

// cleanup removes expired entries
func (c *Cache) cleanup() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	for key, entry := range c.entries {
		if now.After(entry.ExpiresAt) {
			delete(c.entries, key)
		}
	}
}

// startCleanup starts the cleanup goroutine
func (c *Cache) startCleanup() {
	ticker := time.NewTicker(c.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.cleanup()
		case <-c.stopCleanup:
			return
		}
	}
}

// Close stops the cache and saves to disk
func (c *Cache) Close() error {
	close(c.stopCleanup)
	return c.saveToDisk()
}

// loadFromDisk loads cache entries from disk
func (c *Cache) loadFromDisk() error {
	if c.cacheDir == "" {
		return nil
	}

	cacheFile := filepath.Join(c.cacheDir, "cache.json")
	if _, err := os.Stat(cacheFile); os.IsNotExist(err) {
		return nil // No cache file exists yet
	}

	data, err := os.ReadFile(cacheFile)
	if err != nil {
		return fmt.Errorf("failed to read cache file: %w", err)
	}

	var entries map[string]*CacheEntry
	if err := json.Unmarshal(data, &entries); err != nil {
		return fmt.Errorf("failed to unmarshal cache data: %w", err)
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Filter out expired entries
	now := time.Now()
	for key, entry := range entries {
		if now.Before(entry.ExpiresAt) {
			c.entries[key] = entry
		}
	}

	return nil
}

// saveToDisk saves cache entries to disk
func (c *Cache) saveToDisk() error {
	if c.cacheDir == "" {
		return nil
	}

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	data, err := json.MarshalIndent(c.entries, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal cache data: %w", err)
	}

	cacheFile := filepath.Join(c.cacheDir, "cache.json")
	if err := os.WriteFile(cacheFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write cache file: %w", err)
	}

	return nil
}

// CacheKey generates cache keys for common operations
type CacheKey struct{}

// LLMResponse generates a cache key for LLM responses
func (CacheKey) LLMResponse(provider, model string, messages []types.Message) string {
	hash := fmt.Sprintf("%s:%s:%d", provider, model, len(messages))
	if len(messages) > 0 {
		lastMsg := messages[len(messages)-1]
		hash += ":" + lastMsg.Content[:min(50, len(lastMsg.Content))]
	}
	return "llm_response:" + hash
}

// SessionData generates a cache key for session data
func (CacheKey) SessionData(sessionID string) string {
	return "session:" + sessionID
}

// SystemInfo generates a cache key for system information
func (CacheKey) SystemInfo(infoType string) string {
	return "system_info:" + infoType
}

// CommandResult generates a cache key for command results
func (CacheKey) CommandResult(command, workingDir string) string {
	return fmt.Sprintf("command:%s:%s", workingDir, command)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
