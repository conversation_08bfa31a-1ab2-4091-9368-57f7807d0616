package components

import (
	"strings"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/ui/styles"
)

// InputComponent represents the input UI component
type InputComponent struct {
	theme      *styles.Theme
	textInput  textinput.Model
	width      int
	height     int
	focused    bool
	multiline  bool
	placeholder string
	prompt     string
}

// NewInputComponent creates a new input component
func NewInputComponent(theme *styles.Theme) *InputComponent {
	ti := textinput.New()
	ti.Placeholder = "Type your message..."
	ti.CharLimit = 0 // No limit
	ti.Width = 50

	return &InputComponent{
		theme:       theme,
		textInput:   ti,
		placeholder: "Type your message...",
		prompt:      "❯ ",
	}
}

// SetWidth sets the width of the input component
func (i *InputComponent) SetWidth(width int) {
	i.width = width
	// Adjust text input width accounting for prompt and padding
	inputWidth := width - lipgloss.Width(i.prompt) - 4
	if inputWidth > 0 {
		i.textInput.Width = inputWidth
	}
}

// SetHeight sets the height of the input component
func (i *InputComponent) SetHeight(height int) {
	i.height = height
}

// Focus focuses the input component
func (i *InputComponent) Focus() tea.Cmd {
	i.focused = true
	return i.textInput.Focus()
}

// Blur blurs the input component
func (i *InputComponent) Blur() {
	i.focused = false
	i.textInput.Blur()
}

// IsFocused returns whether the input is focused
func (i *InputComponent) IsFocused() bool {
	return i.focused
}

// SetValue sets the input value
func (i *InputComponent) SetValue(value string) {
	i.textInput.SetValue(value)
}

// GetValue returns the current input value
func (i *InputComponent) GetValue() string {
	return i.textInput.Value()
}

// Clear clears the input value
func (i *InputComponent) Clear() {
	i.textInput.SetValue("")
}

// SetPlaceholder sets the placeholder text
func (i *InputComponent) SetPlaceholder(placeholder string) {
	i.placeholder = placeholder
	i.textInput.Placeholder = placeholder
}

// SetPrompt sets the prompt text
func (i *InputComponent) SetPrompt(prompt string) {
	i.prompt = prompt
	// Recalculate width
	if i.width > 0 {
		i.SetWidth(i.width)
	}
}

// Update handles messages for the input component
func (i *InputComponent) Update(msg tea.Msg) tea.Cmd {
	var cmd tea.Cmd
	i.textInput, cmd = i.textInput.Update(msg)
	return cmd
}

// Render renders the input component
func (i *InputComponent) Render() string {
	// Style the prompt
	promptStyle := i.theme.Styles.InputPrompt
	if !i.focused {
		promptStyle = i.theme.Styles.TextMuted
	}
	
	prompt := promptStyle.Render(i.prompt)
	
	// Style the input field
	_ = i.theme.Styles.InputField
	if i.focused {
		_ = i.theme.Styles.InputFocused
	}
	
	// Render the text input
	input := i.textInput.View()
	
	// Combine prompt and input
	content := prompt + input
	
	// Apply container styling
	container := i.theme.Styles.InputContainer
	if i.width > 0 {
		container = container.Width(i.width)
	}
	
	return container.Render(content)
}

// RenderWithSuggestions renders the input with autocomplete suggestions
func (i *InputComponent) RenderWithSuggestions(suggestions []string, selectedIndex int) string {
	baseInput := i.Render()
	
	if len(suggestions) == 0 {
		return baseInput
	}
	
	// Create suggestions list
	var suggestionItems []string
	for idx, suggestion := range suggestions {
		style := i.theme.Styles.SlashMenuItem
		if idx == selectedIndex {
			style = i.theme.Styles.SlashMenuActive
		}
		suggestionItems = append(suggestionItems, style.Render(suggestion))
	}
	
	// Style the suggestions container
	suggestionsContent := strings.Join(suggestionItems, "\n")
	suggestionsContainer := i.theme.Styles.SlashMenu.Render(suggestionsContent)
	
	return baseInput + "\n" + suggestionsContainer
}

// RenderMultiline renders the input as a multiline text area
func (i *InputComponent) RenderMultiline() string {
	// For multiline, we'll simulate it with the current input
	// In a full implementation, you might use a different component
	
	value := i.textInput.Value()
	lines := strings.Split(value, "\n")
	
	// Style each line
	var styledLines []string
	for lineIdx, line := range lines {
		lineStyle := i.theme.Styles.InputField
		if i.focused && lineIdx == len(lines)-1 {
			lineStyle = i.theme.Styles.InputFocused
		}
		styledLines = append(styledLines, lineStyle.Render(line))
	}
	
	// Add prompt to first line
	if len(styledLines) > 0 {
		promptStyle := i.theme.Styles.InputPrompt
		if !i.focused {
			promptStyle = i.theme.Styles.TextMuted
		}
		prompt := promptStyle.Render(i.prompt)
		styledLines[0] = prompt + styledLines[0]
	}
	
	content := strings.Join(styledLines, "\n")
	
	// Apply container styling
	container := i.theme.Styles.InputContainer
	if i.width > 0 {
		container = container.Width(i.width)
	}
	if i.height > 0 {
		container = container.Height(i.height)
	}
	
	return container.Render(content)
}

// GetCursorPosition returns the cursor position
func (i *InputComponent) GetCursorPosition() int {
	return i.textInput.Position()
}

// SetCursorPosition sets the cursor position
func (i *InputComponent) SetCursorPosition(pos int) {
	i.textInput.SetCursor(pos)
}

// InsertText inserts text at the current cursor position
func (i *InputComponent) InsertText(text string) {
	currentValue := i.textInput.Value()
	cursorPos := i.textInput.Position()
	
	newValue := currentValue[:cursorPos] + text + currentValue[cursorPos:]
	i.textInput.SetValue(newValue)
	i.textInput.SetCursor(cursorPos + len(text))
}

// DeleteCharacter deletes the character at the current cursor position
func (i *InputComponent) DeleteCharacter() {
	currentValue := i.textInput.Value()
	cursorPos := i.textInput.Position()
	
	if cursorPos > 0 {
		newValue := currentValue[:cursorPos-1] + currentValue[cursorPos:]
		i.textInput.SetValue(newValue)
		i.textInput.SetCursor(cursorPos - 1)
	}
}

// GetSelectedText returns the selected text (if any)
func (i *InputComponent) GetSelectedText() string {
	// The textinput component doesn't support selection by default
	// This is a placeholder for future implementation
	return ""
}

// HasText returns whether the input has any text
func (i *InputComponent) HasText() bool {
	return strings.TrimSpace(i.textInput.Value()) != ""
}

// GetWordAtCursor returns the word at the current cursor position
func (i *InputComponent) GetWordAtCursor() string {
	value := i.textInput.Value()
	cursorPos := i.textInput.Position()
	
	if cursorPos >= len(value) {
		return ""
	}
	
	// Find word boundaries
	start := cursorPos
	end := cursorPos
	
	// Move start backward to find word start
	for start > 0 && !isWordBoundary(rune(value[start-1])) {
		start--
	}
	
	// Move end forward to find word end
	for end < len(value) && !isWordBoundary(rune(value[end])) {
		end++
	}
	
	if start == end {
		return ""
	}
	
	return value[start:end]
}

// ReplaceWordAtCursor replaces the word at cursor with new text
func (i *InputComponent) ReplaceWordAtCursor(newWord string) {
	value := i.textInput.Value()
	cursorPos := i.textInput.Position()
	
	if cursorPos >= len(value) {
		return
	}
	
	// Find word boundaries
	start := cursorPos
	end := cursorPos
	
	// Move start backward to find word start
	for start > 0 && !isWordBoundary(rune(value[start-1])) {
		start--
	}
	
	// Move end forward to find word end
	for end < len(value) && !isWordBoundary(rune(value[end])) {
		end++
	}
	
	// Replace the word
	newValue := value[:start] + newWord + value[end:]
	i.textInput.SetValue(newValue)
	i.textInput.SetCursor(start + len(newWord))
}

// GetHeight returns the height of the input component
func (i *InputComponent) GetHeight() int {
	if i.multiline {
		// Count lines in current value
		lines := strings.Split(i.textInput.Value(), "\n")
		return len(lines) + 2 // Add padding
	}
	return 3 // Single line with container padding
}

// SetMultiline enables or disables multiline mode
func (i *InputComponent) SetMultiline(multiline bool) {
	i.multiline = multiline
}

// IsMultiline returns whether multiline mode is enabled
func (i *InputComponent) IsMultiline() bool {
	return i.multiline
}

// Helper functions

func isWordBoundary(r rune) bool {
	return r == ' ' || r == '\t' || r == '\n' || r == '\r' || 
		   r == '.' || r == ',' || r == ';' || r == ':' ||
		   r == '!' || r == '?' || r == '(' || r == ')' ||
		   r == '[' || r == ']' || r == '{' || r == '}' ||
		   r == '"' || r == '\'' || r == '`'
}

// InputMode represents different input modes
type InputMode int

const (
	ModeNormal InputMode = iota
	ModeCommand
	ModeSearch
	ModeMultiline
)

// SetMode sets the input mode
func (i *InputComponent) SetMode(mode InputMode) {
	switch mode {
	case ModeNormal:
		i.SetPrompt("❯ ")
		i.SetPlaceholder("Type your message...")
		i.SetMultiline(false)
	case ModeCommand:
		i.SetPrompt("/ ")
		i.SetPlaceholder("Enter command...")
		i.SetMultiline(false)
	case ModeSearch:
		i.SetPrompt("🔍 ")
		i.SetPlaceholder("Search...")
		i.SetMultiline(false)
	case ModeMultiline:
		i.SetPrompt("❯ ")
		i.SetPlaceholder("Type your message... (Ctrl+Enter to send)")
		i.SetMultiline(true)
	}
}
