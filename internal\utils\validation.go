package utils

import (
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"unicode"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Value   interface{}
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation failed for field '%s': %s", e.Field, e.Message)
}

// Validator provides validation functions
type Validator struct {
	errors []ValidationError
}

// NewValidator creates a new validator
func NewValidator() *Validator {
	return &Validator{
		errors: make([]ValidationError, 0),
	}
}

// AddError adds a validation error
func (v *Validator) AddError(field string, value interface{}, message string) {
	v.errors = append(v.errors, ValidationError{
		Field:   field,
		Value:   value,
		Message: message,
	})
}

// HasErrors returns true if there are validation errors
func (v *Validator) HasErrors() bool {
	return len(v.errors) > 0
}

// GetErrors returns all validation errors
func (v *Validator) GetErrors() []ValidationError {
	return v.errors
}

// GetErrorMessages returns all error messages
func (v *Validator) GetErrorMessages() []string {
	messages := make([]string, len(v.errors))
	for i, err := range v.errors {
		messages[i] = err.Error()
	}
	return messages
}

// Clear clears all validation errors
func (v *Validator) Clear() {
	v.errors = v.errors[:0]
}

// String validation functions

// ValidateRequired checks if a string is not empty
func (v *Validator) ValidateRequired(field, value string) {
	if strings.TrimSpace(value) == "" {
		v.AddError(field, value, "is required")
	}
}

// ValidateMinLength checks if a string meets minimum length
func (v *Validator) ValidateMinLength(field, value string, minLength int) {
	if len(value) < minLength {
		v.AddError(field, value, fmt.Sprintf("must be at least %d characters long", minLength))
	}
}

// ValidateMaxLength checks if a string doesn't exceed maximum length
func (v *Validator) ValidateMaxLength(field, value string, maxLength int) {
	if len(value) > maxLength {
		v.AddError(field, value, fmt.Sprintf("must not exceed %d characters", maxLength))
	}
}

// ValidatePattern checks if a string matches a regex pattern
func (v *Validator) ValidatePattern(field, value, pattern, message string) {
	matched, err := regexp.MatchString(pattern, value)
	if err != nil {
		v.AddError(field, value, fmt.Sprintf("pattern validation failed: %v", err))
		return
	}
	if !matched {
		v.AddError(field, value, message)
	}
}

// ValidateEmail checks if a string is a valid email address
func (v *Validator) ValidateEmail(field, value string) {
	emailPattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	v.ValidatePattern(field, value, emailPattern, "must be a valid email address")
}

// ValidateURL checks if a string is a valid URL
func (v *Validator) ValidateURL(field, value string) {
	if value == "" {
		return // Allow empty URLs
	}
	
	_, err := url.Parse(value)
	if err != nil {
		v.AddError(field, value, "must be a valid URL")
	}
}

// ValidateOneOf checks if a value is one of the allowed values
func (v *Validator) ValidateOneOf(field, value string, allowed []string) {
	for _, allowedValue := range allowed {
		if value == allowedValue {
			return
		}
	}
	v.AddError(field, value, fmt.Sprintf("must be one of: %s", strings.Join(allowed, ", ")))
}

// File and path validation functions

// ValidateFilePath checks if a file path is valid and accessible
func (v *Validator) ValidateFilePath(field, path string) {
	if path == "" {
		v.AddError(field, path, "file path is required")
		return
	}
	
	// Check if path is absolute or relative
	if !filepath.IsAbs(path) {
		// Convert to absolute path
		absPath, err := filepath.Abs(path)
		if err != nil {
			v.AddError(field, path, fmt.Sprintf("invalid file path: %v", err))
			return
		}
		path = absPath
	}
	
	// Check if file exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		v.AddError(field, path, "file does not exist")
	}
}

// ValidateDirectoryPath checks if a directory path is valid and accessible
func (v *Validator) ValidateDirectoryPath(field, path string) {
	if path == "" {
		v.AddError(field, path, "directory path is required")
		return
	}
	
	// Check if path is absolute or relative
	if !filepath.IsAbs(path) {
		// Convert to absolute path
		absPath, err := filepath.Abs(path)
		if err != nil {
			v.AddError(field, path, fmt.Sprintf("invalid directory path: %v", err))
			return
		}
		path = absPath
	}
	
	// Check if directory exists
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		v.AddError(field, path, "directory does not exist")
		return
	}
	
	// Check if it's actually a directory
	if !info.IsDir() {
		v.AddError(field, path, "path is not a directory")
	}
}

// ValidatePathSafety checks if a path is safe (no path traversal)
func (v *Validator) ValidatePathSafety(field, path string) {
	if strings.Contains(path, "..") {
		v.AddError(field, path, "path traversal detected")
	}
	
	// Check for other suspicious patterns
	suspiciousPatterns := []string{
		"/etc/passwd",
		"/etc/shadow",
		"C:\\Windows\\System32",
		"~/.ssh",
		"~/.aws",
	}
	
	lowerPath := strings.ToLower(path)
	for _, pattern := range suspiciousPatterns {
		if strings.Contains(lowerPath, strings.ToLower(pattern)) {
			v.AddError(field, path, "access to sensitive path not allowed")
			break
		}
	}
}

// Numeric validation functions

// ValidateRange checks if a number is within a specified range
func (v *Validator) ValidateRange(field string, value, min, max int) {
	if value < min || value > max {
		v.AddError(field, value, fmt.Sprintf("must be between %d and %d", min, max))
	}
}

// ValidatePositive checks if a number is positive
func (v *Validator) ValidatePositive(field string, value int) {
	if value <= 0 {
		v.AddError(field, value, "must be positive")
	}
}

// ValidateNonNegative checks if a number is non-negative
func (v *Validator) ValidateNonNegative(field string, value int) {
	if value < 0 {
		v.AddError(field, value, "must be non-negative")
	}
}

// Command validation functions

// ValidateCommand checks if a command is safe to execute
func (v *Validator) ValidateCommand(field, command string, allowedCommands, blockedCommands []string) {
	if command == "" {
		v.AddError(field, command, "command is required")
		return
	}
	
	lowerCommand := strings.ToLower(command)
	
	// Check against blocked commands
	for _, blocked := range blockedCommands {
		if strings.Contains(lowerCommand, strings.ToLower(blocked)) {
			v.AddError(field, command, fmt.Sprintf("command contains blocked pattern: %s", blocked))
			return
		}
	}
	
	// If allowed commands are specified, check against them
	if len(allowedCommands) > 0 {
		allowed := false
		for _, allowedCmd := range allowedCommands {
			if strings.HasPrefix(lowerCommand, strings.ToLower(allowedCmd)) {
				allowed = true
				break
			}
		}
		if !allowed {
			v.AddError(field, command, "command not in allowed list")
		}
	}
}

// API key validation functions

// ValidateAPIKey checks if an API key has a valid format
func (v *Validator) ValidateAPIKey(field, apiKey, provider string) {
	if apiKey == "" {
		if provider == "deepseek" {
			v.AddError(field, apiKey, "API key is required for Deepseek provider")
		}
		return
	}
	
	switch provider {
	case "deepseek":
		// Deepseek API keys typically start with "sk-"
		if !strings.HasPrefix(apiKey, "sk-") {
			v.AddError(field, apiKey, "Deepseek API key should start with 'sk-'")
		}
		if len(apiKey) < 20 {
			v.AddError(field, apiKey, "API key appears to be too short")
		}
	case "openai":
		// OpenAI API keys typically start with "sk-"
		if !strings.HasPrefix(apiKey, "sk-") {
			v.AddError(field, apiKey, "OpenAI API key should start with 'sk-'")
		}
		if len(apiKey) < 40 {
			v.AddError(field, apiKey, "API key appears to be too short")
		}
	}
}

// Content validation functions

// ValidateNotEmpty checks if content is not empty
func (v *Validator) ValidateNotEmpty(field, content string) {
	if strings.TrimSpace(content) == "" {
		v.AddError(field, content, "content cannot be empty")
	}
}

// ValidateContentLength checks if content length is within limits
func (v *Validator) ValidateContentLength(field, content string, maxLength int) {
	if len(content) > maxLength {
		v.AddError(field, content, fmt.Sprintf("content exceeds maximum length of %d characters", maxLength))
	}
}

// ValidateNoControlCharacters checks if content contains no control characters
func (v *Validator) ValidateNoControlCharacters(field, content string) {
	for _, r := range content {
		if unicode.IsControl(r) && r != '\n' && r != '\r' && r != '\t' {
			v.AddError(field, content, "content contains invalid control characters")
			break
		}
	}
}

// Utility functions

// IsValidIdentifier checks if a string is a valid identifier
func IsValidIdentifier(s string) bool {
	if s == "" {
		return false
	}
	
	// Must start with letter or underscore
	if !unicode.IsLetter(rune(s[0])) && s[0] != '_' {
		return false
	}
	
	// Rest must be letters, digits, or underscores
	for _, r := range s[1:] {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '_' {
			return false
		}
	}
	
	return true
}

// SanitizeFilename removes or replaces invalid characters in a filename
func SanitizeFilename(filename string) string {
	// Replace invalid characters with underscores
	invalidChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	result := filename
	
	for _, char := range invalidChars {
		result = strings.ReplaceAll(result, char, "_")
	}
	
	// Remove leading/trailing spaces and dots
	result = strings.Trim(result, " .")
	
	// Ensure it's not empty
	if result == "" {
		result = "unnamed"
	}
	
	return result
}

// ValidateJSON checks if a string is valid JSON
func (v *Validator) ValidateJSON(field, jsonStr string) {
	if jsonStr == "" {
		return // Allow empty JSON
	}
	
	// Try to parse as JSON
	var js interface{}
	if err := json.Unmarshal([]byte(jsonStr), &js); err != nil {
		v.AddError(field, jsonStr, fmt.Sprintf("invalid JSON: %v", err))
	}
}
