package components

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/ui/styles"
	"arien-ai/internal/utils"
	"arien-ai/pkg/types"
)

// HistoryComponent represents the message history UI component
type HistoryComponent struct {
	theme         *styles.Theme
	viewport      viewport.Model
	messages      []*types.Message
	width         int
	height        int
	showTimestamps bool
	showTokens    bool
	autoScroll    bool
	maxMessages   int
}

// NewHistoryComponent creates a new history component
func NewHistoryComponent(theme *styles.Theme) *HistoryComponent {
	vp := viewport.New(80, 20)
	vp.Style = lipgloss.NewStyle()

	return &HistoryComponent{
		theme:         theme,
		viewport:      vp,
		messages:      make([]*types.Message, 0),
		showTimestamps: true,
		showTokens:    true,
		autoScroll:    true,
		maxMessages:   1000,
	}
}

// SetSize sets the size of the history component
func (h *HistoryComponent) SetSize(width, height int) {
	h.width = width
	h.height = height
	h.viewport.Width = width
	h.viewport.Height = height
}

// SetMessages sets the messages to display
func (h *HistoryComponent) SetMessages(messages []*types.Message) {
	h.messages = messages
	h.updateContent()
}

// AddMessage adds a new message to the history
func (h *HistoryComponent) AddMessage(message *types.Message) {
	h.messages = append(h.messages, message)
	
	// Limit number of messages
	if len(h.messages) > h.maxMessages {
		h.messages = h.messages[len(h.messages)-h.maxMessages:]
	}
	
	h.updateContent()
	
	// Auto-scroll to bottom if enabled
	if h.autoScroll {
		h.viewport.GotoBottom()
	}
}

// UpdateMessage updates an existing message
func (h *HistoryComponent) UpdateMessage(messageID string, updatedMessage *types.Message) {
	for i, msg := range h.messages {
		if msg.ID == messageID {
			h.messages[i] = updatedMessage
			h.updateContent()
			break
		}
	}
}

// RemoveMessage removes a message from the history
func (h *HistoryComponent) RemoveMessage(messageID string) {
	for i, msg := range h.messages {
		if msg.ID == messageID {
			h.messages = append(h.messages[:i], h.messages[i+1:]...)
			h.updateContent()
			break
		}
	}
}

// Clear clears all messages
func (h *HistoryComponent) Clear() {
	h.messages = make([]*types.Message, 0)
	h.updateContent()
}

// Update handles messages for the history component
func (h *HistoryComponent) Update(msg tea.Msg) tea.Cmd {
	var cmd tea.Cmd
	h.viewport, cmd = h.viewport.Update(msg)
	return cmd
}

// Render renders the history component
func (h *HistoryComponent) Render() string {
	return h.theme.Styles.ChatContainer.
		Width(h.width).
		Height(h.height).
		Render(h.viewport.View())
}

// updateContent updates the viewport content with formatted messages
func (h *HistoryComponent) updateContent() {
	var content strings.Builder
	
	for i, message := range h.messages {
		if i > 0 {
			content.WriteString("\n")
		}
		content.WriteString(h.formatMessage(message))
	}
	
	h.viewport.SetContent(content.String())
}

// formatMessage formats a single message for display
func (h *HistoryComponent) formatMessage(message *types.Message) string {
	var parts []string
	
	// Add timestamp if enabled
	if h.showTimestamps {
		timestamp := h.theme.Styles.TextMuted.Render(
			message.Timestamp.Format("15:04:05"),
		)
		parts = append(parts, timestamp)
	}
	
	// Format message based on role and type
	switch message.Role {
	case types.RoleUser:
		parts = append(parts, h.formatUserMessage(message))
	case types.RoleAssistant:
		parts = append(parts, h.formatAssistantMessage(message))
	case types.RoleSystem:
		parts = append(parts, h.formatSystemMessage(message))
	case types.RoleTool:
		parts = append(parts, h.formatToolMessage(message))
	}
	
	// Add token usage if enabled and available
	if h.showTokens && message.TokenUsage != nil && message.TokenUsage.TotalTokens > 0 {
		tokenText := utils.FormatTokenCount(message.TokenUsage.TotalTokens)
		tokenInfo := h.theme.Styles.TextMuted.Render(fmt.Sprintf("(%s)", tokenText))
		parts = append(parts, tokenInfo)
	}
	
	return strings.Join(parts, " ")
}

// formatUserMessage formats a user message
func (h *HistoryComponent) formatUserMessage(message *types.Message) string {
	style := h.theme.Styles.UserMessage
	icon := "👤"
	
	content := h.wrapContent(message.Content)
	return style.Render(fmt.Sprintf("%s %s", icon, content))
}

// formatAssistantMessage formats an assistant message
func (h *HistoryComponent) formatAssistantMessage(message *types.Message) string {
	style := h.theme.Styles.AssistantMessage
	icon := "🤖"
	
	var content strings.Builder
	
	// Add main content
	if message.Content != "" {
		content.WriteString(h.wrapContent(message.Content))
	}
	
	// Add tool calls if present
	if len(message.ToolCalls) > 0 {
		if content.Len() > 0 {
			content.WriteString("\n\n")
		}
		content.WriteString(h.formatToolCalls(message.ToolCalls))
	}
	
	return style.Render(fmt.Sprintf("%s %s", icon, content.String()))
}

// formatSystemMessage formats a system message
func (h *HistoryComponent) formatSystemMessage(message *types.Message) string {
	style := h.theme.Styles.SystemMessage
	icon := "⚙️"
	
	content := h.wrapContent(message.Content)
	return style.Render(fmt.Sprintf("%s %s", icon, content))
}

// formatToolMessage formats a tool execution message
func (h *HistoryComponent) formatToolMessage(message *types.Message) string {
	var style lipgloss.Style
	var icon string
	
	switch message.Type {
	case types.TypeCommand:
		style = h.theme.Styles.CommandText
		icon = "⚡"
	case types.TypeResult:
		if message.ToolResult != nil && message.ToolResult.Success {
			style = h.theme.Styles.SuccessMessage
			icon = "✅"
		} else {
			style = h.theme.Styles.CommandOutput
			icon = "📄"
		}
	case types.TypeError:
		style = h.theme.Styles.ErrorMessage
		icon = "❌"
	default:
		style = h.theme.Styles.TextSecondary
		icon = "🔧"
	}
	
	content := h.wrapContent(message.Content)
	
	// Add tool result details if available
	if message.ToolResult != nil {
		var details []string
		if message.ToolResult.ExitCode != 0 {
			details = append(details, fmt.Sprintf("Exit code: %d", message.ToolResult.ExitCode))
		}
		if message.ToolResult.Duration > 0 {
			details = append(details, fmt.Sprintf("Duration: %s", utils.FormatDuration(message.ToolResult.Duration)))
		}
		
		if len(details) > 0 {
			detailText := h.theme.Styles.TextMuted.Render(fmt.Sprintf("(%s)", strings.Join(details, ", ")))
			content += "\n" + detailText
		}
	}
	
	return style.Render(fmt.Sprintf("%s %s", icon, content))
}

// formatToolCalls formats tool calls for display
func (h *HistoryComponent) formatToolCalls(toolCalls []types.ToolCall) string {
	var calls []string
	
	for _, call := range toolCalls {
		callText := fmt.Sprintf("🔧 %s(%s)", call.Function.Name, string(call.Function.Arguments))
		calls = append(calls, h.theme.Styles.CommandText.Render(callText))
	}
	
	return strings.Join(calls, "\n")
}

// wrapContent wraps content to fit the viewport width
func (h *HistoryComponent) wrapContent(content string) string {
	if h.width <= 0 {
		return content
	}
	
	// Account for padding and margins
	maxWidth := h.width - 10
	if maxWidth <= 0 {
		return content
	}
	
	lines := utils.WrapText(content, maxWidth)
	return strings.Join(lines, "\n")
}

// ScrollUp scrolls the history up
func (h *HistoryComponent) ScrollUp() {
	h.viewport.LineUp(3)
}

// ScrollDown scrolls the history down
func (h *HistoryComponent) ScrollDown() {
	h.viewport.LineDown(3)
}

// ScrollToTop scrolls to the top of the history
func (h *HistoryComponent) ScrollToTop() {
	h.viewport.GotoTop()
}

// ScrollToBottom scrolls to the bottom of the history
func (h *HistoryComponent) ScrollToBottom() {
	h.viewport.GotoBottom()
}

// SetAutoScroll enables or disables auto-scrolling
func (h *HistoryComponent) SetAutoScroll(autoScroll bool) {
	h.autoScroll = autoScroll
}

// SetShowTimestamps enables or disables timestamp display
func (h *HistoryComponent) SetShowTimestamps(show bool) {
	h.showTimestamps = show
	h.updateContent()
}

// SetShowTokens enables or disables token count display
func (h *HistoryComponent) SetShowTokens(show bool) {
	h.showTokens = show
	h.updateContent()
}

// SetMaxMessages sets the maximum number of messages to keep
func (h *HistoryComponent) SetMaxMessages(max int) {
	h.maxMessages = max
	
	// Trim messages if necessary
	if len(h.messages) > max {
		h.messages = h.messages[len(h.messages)-max:]
		h.updateContent()
	}
}

// GetMessageCount returns the number of messages
func (h *HistoryComponent) GetMessageCount() int {
	return len(h.messages)
}

// GetMessages returns all messages
func (h *HistoryComponent) GetMessages() []*types.Message {
	return h.messages
}

// SearchMessages searches for messages containing the query
func (h *HistoryComponent) SearchMessages(query string) []*types.Message {
	query = strings.ToLower(query)
	var results []*types.Message
	
	for _, message := range h.messages {
		if strings.Contains(strings.ToLower(message.Content), query) {
			results = append(results, message)
		}
	}
	
	return results
}

// FilterMessagesByRole filters messages by role
func (h *HistoryComponent) FilterMessagesByRole(role types.MessageRole) []*types.Message {
	var results []*types.Message
	
	for _, message := range h.messages {
		if message.Role == role {
			results = append(results, message)
		}
	}
	
	return results
}

// FilterMessagesByType filters messages by type
func (h *HistoryComponent) FilterMessagesByType(msgType types.MessageType) []*types.Message {
	var results []*types.Message
	
	for _, message := range h.messages {
		if message.Type == msgType {
			results = append(results, message)
		}
	}
	
	return results
}

// FilterMessagesByTimeRange filters messages by time range
func (h *HistoryComponent) FilterMessagesByTimeRange(start, end time.Time) []*types.Message {
	var results []*types.Message
	
	for _, message := range h.messages {
		if message.Timestamp.After(start) && message.Timestamp.Before(end) {
			results = append(results, message)
		}
	}
	
	return results
}

// ExportMessages exports messages to a string format
func (h *HistoryComponent) ExportMessages(format string) string {
	var content strings.Builder
	
	for _, message := range h.messages {
		switch format {
		case "plain":
			content.WriteString(fmt.Sprintf("[%s] %s: %s\n",
				message.Timestamp.Format("2006-01-02 15:04:05"),
				message.Role,
				message.Content,
			))
		case "markdown":
			content.WriteString(fmt.Sprintf("## %s (%s)\n\n%s\n\n",
				message.Role,
				message.Timestamp.Format("2006-01-02 15:04:05"),
				message.Content,
			))
		default:
			content.WriteString(h.formatMessage(message))
			content.WriteString("\n")
		}
	}
	
	return content.String()
}
