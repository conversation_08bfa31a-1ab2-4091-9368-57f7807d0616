#!/bin/bash

# Arien-AI Universal Installation Script
# Supports Windows 11 WSL, macOS, and Linux

set -e

# Configuration
REPO_URL="https://github.com/your-username/arien-ai"
BINARY_NAME="arien-ai"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="$HOME/.arien-ai"
VERSION="latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect operating system and architecture
detect_os() {
    case "$(uname -s)" in
        Linux*)
            if grep -q Microsoft /proc/version 2>/dev/null; then
                OS="wsl"
            else
                OS="linux"
            fi
            ;;
        Darwin*)
            OS="macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            OS="windows"
            ;;
        *)
            log_error "Unsupported operating system: $(uname -s)"
            exit 1
            ;;
    esac

    case "$(uname -m)" in
        x86_64|amd64)
            ARCH="amd64"
            ;;
        arm64|aarch64)
            ARCH="arm64"
            ;;
        armv7l)
            ARCH="arm"
            ;;
        *)
            log_error "Unsupported architecture: $(uname -m)"
            exit 1
            ;;
    esac

    log_info "Detected OS: $OS, Architecture: $ARCH"
}

# Check if running as root (not recommended)
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root is not recommended"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check for curl or wget
    if ! command -v curl >/dev/null 2>&1 && ! command -v wget >/dev/null 2>&1; then
        log_error "curl or wget is required but not installed"
        exit 1
    fi
    
    # Check for tar
    if ! command -v tar >/dev/null 2>&1; then
        log_error "tar is required but not installed"
        exit 1
    fi
    
    log_success "All dependencies satisfied"
}

# Download binary
download_binary() {
    log_info "Downloading Arien-AI binary..."
    
    # Construct download URL
    BINARY_FILE="${BINARY_NAME}-${OS}-${ARCH}"
    if [[ "$OS" == "windows" ]]; then
        BINARY_FILE="${BINARY_FILE}.exe"
    fi
    
    DOWNLOAD_URL="${REPO_URL}/releases/download/${VERSION}/${BINARY_FILE}"
    
    # Create temporary directory
    TMP_DIR=$(mktemp -d)
    cd "$TMP_DIR"
    
    # Download binary
    if command -v curl >/dev/null 2>&1; then
        curl -L -o "$BINARY_NAME" "$DOWNLOAD_URL"
    else
        wget -O "$BINARY_NAME" "$DOWNLOAD_URL"
    fi
    
    # Make executable
    chmod +x "$BINARY_NAME"
    
    log_success "Binary downloaded successfully"
}

# Install binary
install_binary() {
    log_info "Installing binary to $INSTALL_DIR..."
    
    # Check if install directory exists and is writable
    if [[ ! -d "$INSTALL_DIR" ]]; then
        log_info "Creating install directory: $INSTALL_DIR"
        sudo mkdir -p "$INSTALL_DIR"
    fi
    
    if [[ ! -w "$INSTALL_DIR" ]]; then
        log_info "Installing with sudo (requires admin privileges)"
        sudo cp "$BINARY_NAME" "$INSTALL_DIR/"
    else
        cp "$BINARY_NAME" "$INSTALL_DIR/"
    fi
    
    log_success "Binary installed to $INSTALL_DIR/$BINARY_NAME"
}

# Setup configuration directory
setup_config() {
    log_info "Setting up configuration directory..."
    
    if [[ ! -d "$CONFIG_DIR" ]]; then
        mkdir -p "$CONFIG_DIR"
        mkdir -p "$CONFIG_DIR/sessions"
        log_success "Configuration directory created: $CONFIG_DIR"
    else
        log_info "Configuration directory already exists: $CONFIG_DIR"
    fi
}

# Update PATH if necessary
update_path() {
    log_info "Checking PATH configuration..."
    
    # Check if install directory is in PATH
    if [[ ":$PATH:" != *":$INSTALL_DIR:"* ]]; then
        log_warning "$INSTALL_DIR is not in your PATH"
        
        # Determine shell configuration file
        SHELL_CONFIG=""
        case "$SHELL" in
            */bash)
                if [[ -f "$HOME/.bashrc" ]]; then
                    SHELL_CONFIG="$HOME/.bashrc"
                elif [[ -f "$HOME/.bash_profile" ]]; then
                    SHELL_CONFIG="$HOME/.bash_profile"
                fi
                ;;
            */zsh)
                SHELL_CONFIG="$HOME/.zshrc"
                ;;
            */fish)
                SHELL_CONFIG="$HOME/.config/fish/config.fish"
                ;;
        esac
        
        if [[ -n "$SHELL_CONFIG" ]]; then
            read -p "Add $INSTALL_DIR to PATH in $SHELL_CONFIG? (Y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]] || [[ -z $REPLY ]]; then
                echo "export PATH=\"$INSTALL_DIR:\$PATH\"" >> "$SHELL_CONFIG"
                log_success "PATH updated in $SHELL_CONFIG"
                log_info "Please restart your shell or run: source $SHELL_CONFIG"
            fi
        else
            log_warning "Could not determine shell configuration file"
            log_info "Please manually add $INSTALL_DIR to your PATH"
        fi
    else
        log_success "PATH is already configured correctly"
    fi
}

# Verify installation
verify_installation() {
    log_info "Verifying installation..."
    
    if command -v "$BINARY_NAME" >/dev/null 2>&1; then
        VERSION_OUTPUT=$("$BINARY_NAME" version 2>/dev/null || echo "unknown")
        log_success "Installation verified: $VERSION_OUTPUT"
        return 0
    else
        log_error "Installation verification failed"
        log_info "Try running: $INSTALL_DIR/$BINARY_NAME version"
        return 1
    fi
}

# Uninstall function
uninstall() {
    log_info "Uninstalling Arien-AI..."
    
    # Remove binary
    if [[ -f "$INSTALL_DIR/$BINARY_NAME" ]]; then
        if [[ -w "$INSTALL_DIR" ]]; then
            rm "$INSTALL_DIR/$BINARY_NAME"
        else
            sudo rm "$INSTALL_DIR/$BINARY_NAME"
        fi
        log_success "Binary removed from $INSTALL_DIR"
    else
        log_warning "Binary not found in $INSTALL_DIR"
    fi
    
    # Ask about configuration
    if [[ -d "$CONFIG_DIR" ]]; then
        read -p "Remove configuration directory $CONFIG_DIR? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$CONFIG_DIR"
            log_success "Configuration directory removed"
        else
            log_info "Configuration directory preserved"
        fi
    fi
    
    log_success "Uninstallation completed"
}

# Update function
update() {
    log_info "Updating Arien-AI..."
    
    # Check if already installed
    if ! command -v "$BINARY_NAME" >/dev/null 2>&1; then
        log_error "Arien-AI is not currently installed"
        exit 1
    fi
    
    # Get current version
    CURRENT_VERSION=$("$BINARY_NAME" version 2>/dev/null | grep -o 'v[0-9.]*' || echo "unknown")
    log_info "Current version: $CURRENT_VERSION"
    
    # Backup current binary
    BACKUP_DIR="$CONFIG_DIR/backup"
    mkdir -p "$BACKUP_DIR"
    cp "$INSTALL_DIR/$BINARY_NAME" "$BACKUP_DIR/${BINARY_NAME}-${CURRENT_VERSION}-$(date +%Y%m%d)"
    
    # Download and install new version
    download_binary
    install_binary
    
    # Verify update
    if verify_installation; then
        log_success "Update completed successfully"
    else
        log_error "Update failed, restoring backup"
        cp "$BACKUP_DIR/${BINARY_NAME}-${CURRENT_VERSION}-$(date +%Y%m%d)" "$INSTALL_DIR/$BINARY_NAME"
        exit 1
    fi
}

# Show usage
show_usage() {
    echo "Arien-AI Installation Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  install    Install Arien-AI (default)"
    echo "  update     Update existing installation"
    echo "  uninstall  Remove Arien-AI"
    echo ""
    echo "Options:"
    echo "  -v, --version VERSION    Install specific version (default: latest)"
    echo "  -d, --dir DIRECTORY      Install directory (default: /usr/local/bin)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Install latest version"
    echo "  $0 install              # Install latest version"
    echo "  $0 update               # Update to latest version"
    echo "  $0 uninstall            # Remove installation"
    echo "  $0 -v v1.0.0 install    # Install specific version"
}

# Main installation function
main_install() {
    log_info "Starting Arien-AI installation..."
    
    detect_os
    check_root
    check_dependencies
    download_binary
    install_binary
    setup_config
    update_path
    
    if verify_installation; then
        log_success "Arien-AI installed successfully!"
        echo ""
        echo "Next steps:"
        echo "1. Restart your shell or run: source ~/.bashrc (or your shell config)"
        echo "2. Run: arien-ai onboard"
        echo "3. Start using: arien-ai interactive"
    else
        log_error "Installation completed but verification failed"
        exit 1
    fi
}

# Parse command line arguments
COMMAND="install"
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -d|--dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        install|update|uninstall)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Execute command
case "$COMMAND" in
    install)
        main_install
        ;;
    update)
        update
        ;;
    uninstall)
        uninstall
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
