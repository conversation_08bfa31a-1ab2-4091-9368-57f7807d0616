package testing

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"arien-ai/internal/config"
	"arien-ai/internal/executor"
	"arien-ai/internal/llm"
	"arien-ai/internal/session"
	"arien-ai/pkg/types"
)

// TestFramework provides comprehensive testing utilities
type TestFramework struct {
	configManager  *config.Manager
	sessionManager *session.Manager
	executor       *executor.ShellExecutor
	llmClient      llm.Client
	testDir        string
	cleanup        []func()
}

// TestConfig contains test configuration
type TestConfig struct {
	TestDir         string        `yaml:"test_dir"`
	MockLLM         bool          `yaml:"mock_llm"`
	MockExecutor    bool          `yaml:"mock_executor"`
	Timeout         time.Duration `yaml:"timeout"`
	VerboseOutput   bool          `yaml:"verbose_output"`
	KeepTestData    bool          `yaml:"keep_test_data"`
	ParallelTests   bool          `yaml:"parallel_tests"`
}

// DefaultTestConfig returns default test configuration
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		TestDir:       filepath.Join(os.TempDir(), "arien-ai-tests"),
		MockLLM:       true,
		MockExecutor:  true,
		Timeout:       30 * time.Second,
		VerboseOutput: false,
		KeepTestData:  false,
		ParallelTests: true,
	}
}

// NewTestFramework creates a new test framework
func NewTestFramework(config *TestConfig) (*TestFramework, error) {
	// Create test directory
	if err := os.MkdirAll(config.TestDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create test directory: %w", err)
	}

	// Create test config manager
	configManager, err := config.NewManager(filepath.Join(config.TestDir, "config"))
	if err != nil {
		return nil, fmt.Errorf("failed to create config manager: %w", err)
	}

	// Set test configuration
	testConfig := types.DefaultConfig()
	testConfig.LLM.Provider = "mock"
	testConfig.LLM.Model = "mock-model"
	configManager.Set(testConfig)

	// Create session manager
	sessionManager, err := session.NewManager(filepath.Join(config.TestDir, "sessions"))
	if err != nil {
		return nil, fmt.Errorf("failed to create session manager: %w", err)
	}

	// Create executor
	shellExecutor := executor.NewShellExecutor(testConfig.Security)

	// Create LLM client
	var llmClient llm.Client
	if config.MockLLM {
		llmClient = NewMockLLMClient()
	} else {
		llmClient, err = llm.NewClient(testConfig.LLM)
		if err != nil {
			return nil, fmt.Errorf("failed to create LLM client: %w", err)
		}
	}

	framework := &TestFramework{
		configManager:  configManager,
		sessionManager: sessionManager,
		executor:       shellExecutor,
		llmClient:      llmClient,
		testDir:        config.TestDir,
		cleanup:        make([]func(), 0),
	}

	// Add cleanup for test directory
	if !config.KeepTestData {
		framework.AddCleanup(func() {
			os.RemoveAll(config.TestDir)
		})
	}

	return framework, nil
}

// AddCleanup adds a cleanup function to be called when the test framework is closed
func (tf *TestFramework) AddCleanup(fn func()) {
	tf.cleanup = append(tf.cleanup, fn)
}

// Close cleans up the test framework
func (tf *TestFramework) Close() {
	for i := len(tf.cleanup) - 1; i >= 0; i-- {
		tf.cleanup[i]()
	}
}

// CreateTestSession creates a test session
func (tf *TestFramework) CreateTestSession(name string) (*types.Session, error) {
	session := types.NewSession(name, "Test session", tf.configManager.Get().LLM)
	return session, tf.sessionManager.SaveSession(session)
}

// CreateTestMessage creates a test message
func (tf *TestFramework) CreateTestMessage(role types.MessageRole, content string) *types.Message {
	return types.NewMessage(role, types.MessageTypeText, content)
}

// TestCase represents a single test case
type TestCase struct {
	Name        string
	Description string
	Setup       func(*TestFramework) error
	Test        func(*TestFramework) error
	Cleanup     func(*TestFramework) error
	Timeout     time.Duration
	ShouldFail  bool
}

// RunTestCase runs a single test case
func (tf *TestFramework) RunTestCase(t *testing.T, testCase TestCase) {
	t.Run(testCase.Name, func(t *testing.T) {
		// Set timeout
		timeout := testCase.Timeout
		if timeout == 0 {
			timeout = 30 * time.Second
		}

		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()

		// Run test with timeout
		done := make(chan error, 1)
		go func() {
			done <- tf.runTestCaseInternal(testCase)
		}()

		select {
		case err := <-done:
			if testCase.ShouldFail {
				if err == nil {
					t.Errorf("Test %s should have failed but passed", testCase.Name)
				}
			} else {
				if err != nil {
					t.Errorf("Test %s failed: %v", testCase.Name, err)
				}
			}
		case <-ctx.Done():
			t.Errorf("Test %s timed out after %v", testCase.Name, timeout)
		}
	})
}

// runTestCaseInternal runs the test case internally
func (tf *TestFramework) runTestCaseInternal(testCase TestCase) error {
	// Setup
	if testCase.Setup != nil {
		if err := testCase.Setup(tf); err != nil {
			return fmt.Errorf("setup failed: %w", err)
		}
	}

	// Cleanup
	defer func() {
		if testCase.Cleanup != nil {
			testCase.Cleanup(tf)
		}
	}()

	// Run test
	if testCase.Test != nil {
		return testCase.Test(tf)
	}

	return nil
}

// RunTestSuite runs a collection of test cases
func (tf *TestFramework) RunTestSuite(t *testing.T, testCases []TestCase) {
	for _, testCase := range testCases {
		tf.RunTestCase(t, testCase)
	}
}

// MockLLMClient provides a mock LLM client for testing
type MockLLMClient struct {
	responses map[string]*types.Message
	callCount int
}

// NewMockLLMClient creates a new mock LLM client
func NewMockLLMClient() *MockLLMClient {
	return &MockLLMClient{
		responses: make(map[string]*types.Message),
	}
}

// Chat implements the LLM client interface
func (m *MockLLMClient) Chat(ctx context.Context, messages []types.Message, tools []llm.Tool) (*types.Message, error) {
	m.callCount++

	// Get the last user message
	var lastUserMessage string
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == types.RoleUser {
			lastUserMessage = messages[i].Content
			break
		}
	}

	// Check for predefined responses
	if response, exists := m.responses[lastUserMessage]; exists {
		return response, nil
	}

	// Default response
	return types.NewMessage(types.RoleAssistant, types.MessageTypeText,
		fmt.Sprintf("Mock response to: %s", lastUserMessage)), nil
}

// SetResponse sets a predefined response for a specific input
func (m *MockLLMClient) SetResponse(input string, response *types.Message) {
	m.responses[input] = response
}

// GetCallCount returns the number of times Chat was called
func (m *MockLLMClient) GetCallCount() int {
	return m.callCount
}

// Reset resets the mock client state
func (m *MockLLMClient) Reset() {
	m.responses = make(map[string]*types.Message)
	m.callCount = 0
}

// TestAssertions provides test assertion utilities
type TestAssertions struct {
	t *testing.T
}

// NewTestAssertions creates new test assertions
func NewTestAssertions(t *testing.T) *TestAssertions {
	return &TestAssertions{t: t}
}

// AssertEqual asserts that two values are equal
func (ta *TestAssertions) AssertEqual(expected, actual interface{}, message string) {
	if expected != actual {
		ta.t.Errorf("%s: expected %v, got %v", message, expected, actual)
	}
}

// AssertNotEqual asserts that two values are not equal
func (ta *TestAssertions) AssertNotEqual(expected, actual interface{}, message string) {
	if expected == actual {
		ta.t.Errorf("%s: expected %v to not equal %v", message, expected, actual)
	}
}

// AssertNil asserts that a value is nil
func (ta *TestAssertions) AssertNil(value interface{}, message string) {
	if value != nil {
		ta.t.Errorf("%s: expected nil, got %v", message, value)
	}
}

// AssertNotNil asserts that a value is not nil
func (ta *TestAssertions) AssertNotNil(value interface{}, message string) {
	if value == nil {
		ta.t.Errorf("%s: expected non-nil value", message)
	}
}

// AssertContains asserts that a string contains a substring
func (ta *TestAssertions) AssertContains(haystack, needle, message string) {
	if !strings.Contains(haystack, needle) {
		ta.t.Errorf("%s: expected '%s' to contain '%s'", message, haystack, needle)
	}
}

// AssertNotContains asserts that a string does not contain a substring
func (ta *TestAssertions) AssertNotContains(haystack, needle, message string) {
	if strings.Contains(haystack, needle) {
		ta.t.Errorf("%s: expected '%s' to not contain '%s'", message, haystack, needle)
	}
}

// AssertTrue asserts that a condition is true
func (ta *TestAssertions) AssertTrue(condition bool, message string) {
	if !condition {
		ta.t.Errorf("%s: expected true", message)
	}
}

// AssertFalse asserts that a condition is false
func (ta *TestAssertions) AssertFalse(condition bool, message string) {
	if condition {
		ta.t.Errorf("%s: expected false", message)
	}
}

// BenchmarkFramework provides benchmarking utilities
type BenchmarkFramework struct {
	testFramework *TestFramework
}

// NewBenchmarkFramework creates a new benchmark framework
func NewBenchmarkFramework(testFramework *TestFramework) *BenchmarkFramework {
	return &BenchmarkFramework{
		testFramework: testFramework,
	}
}

// BenchmarkLLMResponse benchmarks LLM response time
func (bf *BenchmarkFramework) BenchmarkLLMResponse(b *testing.B, message string) {
	ctx := context.Background()
	messages := []types.Message{
		*types.NewUserMessage(message),
	}
	tools := llm.GetAllTools()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := bf.testFramework.llmClient.Chat(ctx, messages, tools)
		if err != nil {
			b.Fatalf("LLM chat failed: %v", err)
		}
	}
}

// BenchmarkCommandExecution benchmarks command execution time
func (bf *BenchmarkFramework) BenchmarkCommandExecution(b *testing.B, command string) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result := bf.testFramework.executor.ExecuteCommand(command, ".", 30*time.Second, true)
		if !result.Success {
			b.Fatalf("Command execution failed: %s", result.Error)
		}
	}
}
