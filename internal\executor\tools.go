package executor

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// ToolExecutor handles execution of LLM tool calls
type ToolExecutor struct {
	shellExecutor *ShellExecutor
	safetyChecker *SafetyChecker
	formatter     *OutputFormatter
}

// NewToolExecutor creates a new tool executor
func NewToolExecutor(shellExecutor *ShellExecutor) *ToolExecutor {
	return &ToolExecutor{
		shellExecutor: shellExecutor,
		safetyChecker: NewSafetyChecker(false),
		formatter:     NewOutputFormatter(),
	}
}

// ExecuteToolCall executes a single tool call
func (t *ToolExecutor) ExecuteToolCall(ctx context.Context, toolCall types.ToolCall) *types.ToolResult {
	startTime := time.Now()
	
	result := &types.ToolResult{
		ToolCallID: toolCall.ID,
		Success:    false,
	}
	
	// Parse arguments
	var args map[string]interface{}
	if err := json.Unmarshal(toolCall.Function.Arguments, &args); err != nil {
		result.Error = fmt.Sprintf("Failed to parse arguments: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Execute based on function name
	switch toolCall.Function.Name {
	case "execute_shell_command":
		return t.executeShellCommand(ctx, args, startTime)
	case "read_file":
		return t.readFile(ctx, args, startTime)
	case "write_file":
		return t.writeFile(ctx, args, startTime)
	case "get_system_info":
		return t.getSystemInfo(ctx, args, startTime)
	default:
		result.Error = fmt.Sprintf("Unknown function: %s", toolCall.Function.Name)
		result.Duration = time.Since(startTime)
		return result
	}
}

// executeShellCommand executes a shell command
func (t *ToolExecutor) executeShellCommand(ctx context.Context, args map[string]interface{}, startTime time.Time) *types.ToolResult {
	result := &types.ToolResult{
		Success: false,
	}
	
	// Extract parameters
	command, ok := args["command"].(string)
	if !ok {
		result.Error = "command parameter is required"
		result.Duration = time.Since(startTime)
		return result
	}
	
	workingDir, _ := args["working_directory"].(string)
	if workingDir == "" {
		workingDir = "."
	}
	
	timeout := 30 * time.Second
	if timeoutVal, ok := args["timeout"].(float64); ok {
		timeout = time.Duration(timeoutVal) * time.Second
	}
	
	captureOutput := true
	if captureVal, ok := args["capture_output"].(bool); ok {
		captureOutput = captureVal
	}
	
	requireConfirm := false
	if confirmVal, ok := args["require_confirmation"].(bool); ok {
		requireConfirm = confirmVal
	}
	
	// Extract environment variables
	var envVars []string
	if envMap, ok := args["environment"].(map[string]interface{}); ok {
		for key, value := range envMap {
			if strValue, ok := value.(string); ok {
				envVars = append(envVars, fmt.Sprintf("%s=%s", key, strValue))
			}
		}
	}
	
	// Create command execution
	execution := &types.CommandExecution{
		Command:          command,
		WorkingDirectory: workingDir,
		Environment:      envVars,
		Timeout:          timeout,
		CaptureOutput:    captureOutput,
		RequireConfirm:   requireConfirm,
	}
	
	// Execute command
	err := t.shellExecutor.Execute(ctx, execution)
	
	// Create result
	result.Success = execution.Success
	result.ExitCode = execution.ExitCode
	result.Duration = execution.Duration
	
	if err != nil {
		result.Error = err.Error()
	}
	
	// Format output
	if execution.CaptureOutput {
		result.Content = t.formatter.FormatCommandOutput(
			execution.Stdout,
			execution.Stderr,
			execution.ExitCode,
			execution.Duration,
		)
	} else {
		result.Content = fmt.Sprintf("Command executed with exit code %d", execution.ExitCode)
	}
	
	return result
}

// readFile reads a file
func (t *ToolExecutor) readFile(ctx context.Context, args map[string]interface{}, startTime time.Time) *types.ToolResult {
	result := &types.ToolResult{
		Success: false,
	}
	
	// Extract parameters
	filePath, ok := args["file_path"].(string)
	if !ok {
		result.Error = "file_path parameter is required"
		result.Duration = time.Since(startTime)
		return result
	}
	
	encoding, _ := args["encoding"].(string)
	if encoding == "" {
		encoding = "utf-8"
	}
	
	maxSize := int64(1048576) // 1MB default
	if sizeVal, ok := args["max_size"].(float64); ok {
		maxSize = int64(sizeVal)
	}
	
	// Check file safety
	if err := t.safetyChecker.CheckPath(filePath); err != nil {
		result.Error = fmt.Sprintf("File access denied: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Check file size
	info, err := os.Stat(filePath)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to stat file: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	if info.Size() > maxSize {
		result.Error = fmt.Sprintf("File size (%d bytes) exceeds maximum (%d bytes)", info.Size(), maxSize)
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Handle line range if specified
	if lineRangeVal, ok := args["line_range"].([]interface{}); ok && len(lineRangeVal) == 2 {
		startLine, _ := lineRangeVal[0].(float64)
		endLine, _ := lineRangeVal[1].(float64)
		
		content, err := t.readFileLines(filePath, int(startLine), int(endLine))
		if err != nil {
			result.Error = fmt.Sprintf("Failed to read file lines: %v", err)
		} else {
			result.Content = content
			result.Success = true
		}
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Read entire file
	data, err := os.ReadFile(filePath)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to read file: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	result.Content = string(data)
	result.Success = true
	result.Duration = time.Since(startTime)
	return result
}

// writeFile writes content to a file
func (t *ToolExecutor) writeFile(ctx context.Context, args map[string]interface{}, startTime time.Time) *types.ToolResult {
	result := &types.ToolResult{
		Success: false,
	}
	
	// Extract parameters
	filePath, ok := args["file_path"].(string)
	if !ok {
		result.Error = "file_path parameter is required"
		result.Duration = time.Since(startTime)
		return result
	}
	
	content, ok := args["content"].(string)
	if !ok {
		result.Error = "content parameter is required"
		result.Duration = time.Since(startTime)
		return result
	}
	
	mode, _ := args["mode"].(string)
	if mode == "" {
		mode = "write"
	}
	
	backup := true
	if backupVal, ok := args["backup"].(bool); ok {
		backup = backupVal
	}
	
	permissions, _ := args["permissions"].(string)
	if permissions == "" {
		permissions = "644"
	}
	
	// Parse permissions
	perm, err := strconv.ParseUint(permissions, 8, 32)
	if err != nil {
		result.Error = fmt.Sprintf("Invalid permissions: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Check file safety
	if err := t.safetyChecker.CheckFileOperation(mode, filePath); err != nil {
		result.Error = fmt.Sprintf("File operation denied: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Create backup if requested and file exists
	if backup && mode == "write" {
		if _, err := os.Stat(filePath); err == nil {
			backupPath := filePath + ".backup." + time.Now().Format("20060102150405")
			if err := t.copyFile(filePath, backupPath); err != nil {
				result.Error = fmt.Sprintf("Failed to create backup: %v", err)
				result.Duration = time.Since(startTime)
				return result
			}
		}
	}
	
	// Ensure directory exists
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		result.Error = fmt.Sprintf("Failed to create directory: %v", err)
		result.Duration = time.Since(startTime)
		return result
	}
	
	// Write file
	var writeErr error
	if mode == "append" {
		file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, os.FileMode(perm))
		if err != nil {
			writeErr = err
		} else {
			_, writeErr = file.WriteString(content)
			file.Close()
		}
	} else {
		writeErr = os.WriteFile(filePath, []byte(content), os.FileMode(perm))
	}
	
	if writeErr != nil {
		result.Error = fmt.Sprintf("Failed to write file: %v", writeErr)
		result.Duration = time.Since(startTime)
		return result
	}
	
	result.Content = fmt.Sprintf("Successfully wrote %d bytes to %s", len(content), filePath)
	result.Success = true
	result.Duration = time.Since(startTime)
	return result
}

// getSystemInfo retrieves system information
func (t *ToolExecutor) getSystemInfo(ctx context.Context, args map[string]interface{}, startTime time.Time) *types.ToolResult {
	result := &types.ToolResult{
		Success: false,
	}
	
	infoType, _ := args["info_type"].(string)
	if infoType == "" {
		infoType = "basic"
	}
	
	includeSensitive := false
	if sensitiveVal, ok := args["include_sensitive"].(bool); ok {
		includeSensitive = sensitiveVal
	}
	
	var info strings.Builder
	
	switch infoType {
	case "basic", "all":
		info.WriteString(fmt.Sprintf("OS: %s\n", runtime.GOOS))
		info.WriteString(fmt.Sprintf("Architecture: %s\n", runtime.GOARCH))
		info.WriteString(fmt.Sprintf("Go Version: %s\n", runtime.Version()))
		
		if hostname, err := os.Hostname(); err == nil {
			info.WriteString(fmt.Sprintf("Hostname: %s\n", hostname))
		}
		
		if wd, err := os.Getwd(); err == nil {
			info.WriteString(fmt.Sprintf("Working Directory: %s\n", wd))
		}
		
		if infoType == "basic" {
			break
		}
		fallthrough
		
	case "hardware":
		info.WriteString(fmt.Sprintf("CPU Cores: %d\n", runtime.NumCPU()))
		// Add more hardware info as needed
		
	case "environment":
		if includeSensitive {
			for _, env := range os.Environ() {
				info.WriteString(fmt.Sprintf("%s\n", env))
			}
		} else {
			// Only show non-sensitive environment variables
			safeVars := []string{"PATH", "HOME", "USER", "SHELL", "TERM"}
			for _, varName := range safeVars {
				if value := os.Getenv(varName); value != "" {
					info.WriteString(fmt.Sprintf("%s=%s\n", varName, value))
				}
			}
		}
		
	case "network":
		info.WriteString("Network information not implemented\n")
		
	case "processes":
		info.WriteString("Process information not implemented\n")
	}
	
	result.Content = info.String()
	result.Success = true
	result.Duration = time.Since(startTime)
	return result
}

// Helper methods

func (t *ToolExecutor) readFileLines(filePath string, startLine, endLine int) (string, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	
	lines := strings.Split(string(data), "\n")
	
	// Adjust for 1-based indexing
	startLine--
	endLine--
	
	if startLine < 0 {
		startLine = 0
	}
	if endLine >= len(lines) {
		endLine = len(lines) - 1
	}
	if startLine > endLine {
		return "", fmt.Errorf("invalid line range")
	}
	
	return strings.Join(lines[startLine:endLine+1], "\n"), nil
}

func (t *ToolExecutor) copyFile(src, dst string) error {
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}
	return os.WriteFile(dst, data, 0644)
}
