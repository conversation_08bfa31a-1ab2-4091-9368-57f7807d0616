package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"

	"arien-ai/pkg/types"
)

const (
	ConfigFileName = "config.yaml"
	ConfigDirName  = ".arien-ai"
)

// Manager handles configuration management
type Manager struct {
	config     *types.Config
	configPath string
	configDir  string
}

// NewManager creates a new configuration manager
func NewManager() (*Manager, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get user home directory: %w", err)
	}

	configDir := filepath.Join(homeDir, ConfigDirName)
	configPath := filepath.Join(configDir, ConfigFileName)

	manager := &Manager{
		configPath: configPath,
		configDir:  configDir,
	}

	// Ensure config directory exists
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}

	// Load or create default configuration
	if err := manager.load(); err != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	return manager, nil
}

// load loads the configuration from file or creates a default one
func (m *Manager) load() error {
	// Check if config file exists
	if _, err := os.Stat(m.configPath); os.IsNotExist(err) {
		// Create default configuration
		m.config = types.DefaultConfig()
		return m.Save()
	}

	// Load existing configuration
	viper.SetConfigFile(m.configPath)
	viper.SetConfigType("yaml")

	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	config := types.DefaultConfig()
	if err := viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	m.config = config
	return nil
}

// Save saves the current configuration to file
func (m *Manager) Save() error {
	data, err := yaml.Marshal(m.config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(m.configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// Get returns the current configuration
func (m *Manager) Get() *types.Config {
	return m.config
}

// Set updates the configuration
func (m *Manager) Set(config *types.Config) error {
	m.config = config
	return m.Save()
}

// GetConfigDir returns the configuration directory path
func (m *Manager) GetConfigDir() string {
	return m.configDir
}



// GetConfigPath returns the configuration file path
func (m *Manager) GetConfigPath() string {
	return m.configPath
}

// UpdateLLMConfig updates the LLM configuration
func (m *Manager) UpdateLLMConfig(llmConfig types.LLMConfig) error {
	m.config.LLM = llmConfig
	return m.Save()
}

// UpdateUIConfig updates the UI configuration
func (m *Manager) UpdateUIConfig(uiConfig types.UIConfig) error {
	m.config.UI = uiConfig
	return m.Save()
}

// UpdateSessionConfig updates the session configuration
func (m *Manager) UpdateSessionConfig(sessionConfig types.SessionConfig) error {
	m.config.Session = sessionConfig
	return m.Save()
}

// UpdateSecurityConfig updates the security configuration
func (m *Manager) UpdateSecurityConfig(securityConfig types.SecurityConfig) error {
	m.config.Security = securityConfig
	return m.Save()
}

// SetLLMProvider sets the LLM provider and model
func (m *Manager) SetLLMProvider(provider, model string) error {
	m.config.LLM.Provider = provider
	m.config.LLM.Model = model
	return m.Save()
}

// SetAPIKey sets the API key for the current provider
func (m *Manager) SetAPIKey(apiKey string) error {
	m.config.LLM.APIKey = apiKey
	return m.Save()
}

// SetBaseURL sets the base URL for the current provider
func (m *Manager) SetBaseURL(baseURL string) error {
	m.config.LLM.BaseURL = baseURL
	return m.Save()
}

// GetSessionDirectory returns the session directory path
func (m *Manager) GetSessionDirectory() string {
	sessionDir := m.config.Session.SessionDirectory
	if sessionDir == "" || sessionDir == "~/.arien-ai/sessions" {
		return filepath.Join(m.configDir, "sessions")
	}
	
	// Expand home directory if needed
	if sessionDir[0] == '~' {
		homeDir, _ := os.UserHomeDir()
		sessionDir = filepath.Join(homeDir, sessionDir[1:])
	}
	
	return sessionDir
}

// EnsureSessionDirectory ensures the session directory exists
func (m *Manager) EnsureSessionDirectory() error {
	sessionDir := m.GetSessionDirectory()
	return os.MkdirAll(sessionDir, 0755)
}

// IsFirstRun checks if this is the first run (no config file exists)
func (m *Manager) IsFirstRun() bool {
	_, err := os.Stat(m.configPath)
	return os.IsNotExist(err)
}

// Reset resets the configuration to defaults
func (m *Manager) Reset() error {
	m.config = types.DefaultConfig()
	return m.Save()
}

// Validate validates the current configuration
func (m *Manager) Validate() error {
	config := m.config

	// Validate LLM configuration
	if config.LLM.Provider == "" {
		return fmt.Errorf("LLM provider is required")
	}

	if config.LLM.Model == "" {
		return fmt.Errorf("LLM model is required")
	}

	if config.LLM.Provider == "deepseek" && config.LLM.APIKey == "" {
		return fmt.Errorf("API key is required for Deepseek provider")
	}

	if config.LLM.Provider == "ollama" && config.LLM.BaseURL == "" {
		config.LLM.BaseURL = "http://localhost:11434"
	}

	// Validate UI configuration
	if config.UI.MaxHistorySize <= 0 {
		config.UI.MaxHistorySize = 1000
	}

	if config.UI.AnimationSpeed <= 0 {
		config.UI.AnimationSpeed = 100
	}

	// Validate session configuration
	if config.Session.MaxSessions <= 0 {
		config.Session.MaxSessions = 50
	}

	if config.Session.ContextWindow <= 0 {
		config.Session.ContextWindow = 10
	}

	return nil
}

// GetLLMConfig returns the LLM configuration
func (m *Manager) GetLLMConfig() types.LLMConfig {
	return m.config.LLM
}

// GetUIConfig returns the UI configuration
func (m *Manager) GetUIConfig() types.UIConfig {
	return m.config.UI
}

// GetSessionConfig returns the session configuration
func (m *Manager) GetSessionConfig() types.SessionConfig {
	return m.config.Session
}

// GetSecurityConfig returns the security configuration
func (m *Manager) GetSecurityConfig() types.SecurityConfig {
	return m.config.Security
}
