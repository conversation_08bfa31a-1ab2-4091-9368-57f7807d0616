package utils

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"
)

// PerformanceMonitor tracks application performance metrics
type PerformanceMonitor struct {
	metrics map[string]*Metric
	mutex   sync.RWMutex
	started time.Time
}

// Metric represents a performance metric
type Metric struct {
	Name        string        `json:"name"`
	Count       int64         `json:"count"`
	TotalTime   time.Duration `json:"total_time"`
	MinTime     time.Duration `json:"min_time"`
	MaxTime     time.Duration `json:"max_time"`
	AvgTime     time.Duration `json:"avg_time"`
	LastUpdated time.Time     `json:"last_updated"`
}

// NewPerformanceMonitor creates a new performance monitor
func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics: make(map[string]*Metric),
		started: time.Now(),
	}
}

// StartTimer starts a timer for a metric
func (pm *PerformanceMonitor) StartTimer(name string) func() {
	start := time.Now()
	return func() {
		pm.RecordDuration(name, time.Since(start))
	}
}

// RecordDuration records a duration for a metric
func (pm *PerformanceMonitor) RecordDuration(name string, duration time.Duration) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	metric, exists := pm.metrics[name]
	if !exists {
		metric = &Metric{
			Name:    name,
			MinTime: duration,
			MaxTime: duration,
		}
		pm.metrics[name] = metric
	}

	metric.Count++
	metric.TotalTime += duration
	metric.AvgTime = metric.TotalTime / time.Duration(metric.Count)
	metric.LastUpdated = time.Now()

	if duration < metric.MinTime {
		metric.MinTime = duration
	}
	if duration > metric.MaxTime {
		metric.MaxTime = duration
	}
}

// GetMetric returns a metric by name
func (pm *PerformanceMonitor) GetMetric(name string) (*Metric, bool) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	metric, exists := pm.metrics[name]
	if !exists {
		return nil, false
	}

	// Return a copy to avoid race conditions
	return &Metric{
		Name:        metric.Name,
		Count:       metric.Count,
		TotalTime:   metric.TotalTime,
		MinTime:     metric.MinTime,
		MaxTime:     metric.MaxTime,
		AvgTime:     metric.AvgTime,
		LastUpdated: metric.LastUpdated,
	}, true
}

// GetAllMetrics returns all metrics
func (pm *PerformanceMonitor) GetAllMetrics() map[string]*Metric {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	result := make(map[string]*Metric)
	for name, metric := range pm.metrics {
		result[name] = &Metric{
			Name:        metric.Name,
			Count:       metric.Count,
			TotalTime:   metric.TotalTime,
			MinTime:     metric.MinTime,
			MaxTime:     metric.MaxTime,
			AvgTime:     metric.AvgTime,
			LastUpdated: metric.LastUpdated,
		}
	}
	return result
}

// Reset resets all metrics
func (pm *PerformanceMonitor) Reset() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.metrics = make(map[string]*Metric)
	pm.started = time.Now()
}

// GetUptime returns the uptime of the monitor
func (pm *PerformanceMonitor) GetUptime() time.Duration {
	return time.Since(pm.started)
}

// ResourceMonitor tracks system resource usage
type ResourceMonitor struct {
	cpuUsage    float64
	memUsage    float64
	goroutines  int
	lastUpdate  time.Time
	updateMutex sync.RWMutex
}

// NewResourceMonitor creates a new resource monitor
func NewResourceMonitor() *ResourceMonitor {
	rm := &ResourceMonitor{}
	rm.Update()
	return rm
}

// Update updates the resource usage metrics
func (rm *ResourceMonitor) Update() {
	rm.updateMutex.Lock()
	defer rm.updateMutex.Unlock()

	// Get memory stats
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// Calculate memory usage percentage (approximate)
	rm.memUsage = float64(memStats.Alloc) / float64(memStats.Sys) * 100

	// Get goroutine count
	rm.goroutines = runtime.NumGoroutine()

	rm.lastUpdate = time.Now()
}

// GetCPUUsage returns the current CPU usage percentage
func (rm *ResourceMonitor) GetCPUUsage() float64 {
	rm.updateMutex.RLock()
	defer rm.updateMutex.RUnlock()
	return rm.cpuUsage
}

// GetMemoryUsage returns the current memory usage percentage
func (rm *ResourceMonitor) GetMemoryUsage() float64 {
	rm.updateMutex.RLock()
	defer rm.updateMutex.RUnlock()
	return rm.memUsage
}

// GetGoroutineCount returns the current number of goroutines
func (rm *ResourceMonitor) GetGoroutineCount() int {
	rm.updateMutex.RLock()
	defer rm.updateMutex.RUnlock()
	return rm.goroutines
}

// GetLastUpdate returns the time of the last update
func (rm *ResourceMonitor) GetLastUpdate() time.Time {
	rm.updateMutex.RLock()
	defer rm.updateMutex.RUnlock()
	return rm.lastUpdate
}

// RateLimiter implements a token bucket rate limiter
type RateLimiter struct {
	tokens    int
	maxTokens int
	refillRate time.Duration
	lastRefill time.Time
	mutex     sync.Mutex
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(maxTokens int, refillRate time.Duration) *RateLimiter {
	return &RateLimiter{
		tokens:     maxTokens,
		maxTokens:  maxTokens,
		refillRate: refillRate,
		lastRefill: time.Now(),
	}
}

// Allow checks if an operation is allowed
func (rl *RateLimiter) Allow() bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	rl.refill()

	if rl.tokens > 0 {
		rl.tokens--
		return true
	}
	return false
}

// Wait waits until an operation is allowed
func (rl *RateLimiter) Wait(ctx context.Context) error {
	for {
		if rl.Allow() {
			return nil
		}

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(rl.refillRate / 10):
			// Check again after a short delay
		}
	}
}

// refill adds tokens to the bucket
func (rl *RateLimiter) refill() {
	now := time.Now()
	elapsed := now.Sub(rl.lastRefill)
	tokensToAdd := int(elapsed / rl.refillRate)

	if tokensToAdd > 0 {
		rl.tokens += tokensToAdd
		if rl.tokens > rl.maxTokens {
			rl.tokens = rl.maxTokens
		}
		rl.lastRefill = now
	}
}

// CircuitBreaker implements a circuit breaker pattern
type CircuitBreaker struct {
	maxFailures   int
	resetTimeout  time.Duration
	failures      int
	lastFailTime  time.Time
	state         CircuitState
	mutex         sync.RWMutex
}

// CircuitState represents the state of a circuit breaker
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitClosed,
	}
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn func() error) error {
	if !cb.canExecute() {
		return fmt.Errorf("circuit breaker is open")
	}

	err := fn()
	cb.recordResult(err)
	return err
}

// canExecute checks if the function can be executed
func (cb *CircuitBreaker) canExecute() bool {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	switch cb.state {
	case CircuitClosed:
		return true
	case CircuitOpen:
		if time.Since(cb.lastFailTime) > cb.resetTimeout {
			cb.mutex.RUnlock()
			cb.mutex.Lock()
			cb.state = CircuitHalfOpen
			cb.mutex.Unlock()
			cb.mutex.RLock()
			return true
		}
		return false
	case CircuitHalfOpen:
		return true
	default:
		return false
	}
}

// recordResult records the result of an execution
func (cb *CircuitBreaker) recordResult(err error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()

		if cb.failures >= cb.maxFailures {
			cb.state = CircuitOpen
		}
	} else {
		cb.failures = 0
		cb.state = CircuitClosed
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitState {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// GetFailures returns the current failure count
func (cb *CircuitBreaker) GetFailures() int {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.failures
}

// ConnectionPool manages a pool of connections
type ConnectionPool struct {
	connections chan interface{}
	factory     func() (interface{}, error)
	cleanup     func(interface{})
	maxSize     int
	currentSize int
	mutex       sync.Mutex
}

// NewConnectionPool creates a new connection pool
func NewConnectionPool(maxSize int, factory func() (interface{}, error), cleanup func(interface{})) *ConnectionPool {
	return &ConnectionPool{
		connections: make(chan interface{}, maxSize),
		factory:     factory,
		cleanup:     cleanup,
		maxSize:     maxSize,
	}
}

// Get gets a connection from the pool
func (cp *ConnectionPool) Get() (interface{}, error) {
	select {
	case conn := <-cp.connections:
		return conn, nil
	default:
		cp.mutex.Lock()
		defer cp.mutex.Unlock()

		if cp.currentSize < cp.maxSize {
			conn, err := cp.factory()
			if err != nil {
				return nil, err
			}
			cp.currentSize++
			return conn, nil
		}

		// Wait for a connection to become available
		return <-cp.connections, nil
	}
}

// Put returns a connection to the pool
func (cp *ConnectionPool) Put(conn interface{}) {
	select {
	case cp.connections <- conn:
	default:
		// Pool is full, cleanup the connection
		if cp.cleanup != nil {
			cp.cleanup(conn)
		}
		cp.mutex.Lock()
		cp.currentSize--
		cp.mutex.Unlock()
	}
}

// Close closes the connection pool
func (cp *ConnectionPool) Close() {
	close(cp.connections)
	for conn := range cp.connections {
		if cp.cleanup != nil {
			cp.cleanup(conn)
		}
	}
}

// Size returns the current size of the pool
func (cp *ConnectionPool) Size() int {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()
	return cp.currentSize
}
