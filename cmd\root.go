package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	"arien-ai/internal/config"
)

var (
	cfgFile string
	configManager *config.Manager
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "arien-ai",
	Short: "A powerful CLI terminal system with LLM integration",
	Long: `Arien-AI is a modern CLI terminal system that integrates with LLM providers
(Deepseek and Ollama) to execute shell commands intelligently and complete user tasks
through function calling.

Features:
- LLM-powered command execution and task completion
- Support for Deepseek and Ollama providers
- Advanced safety checks and command validation
- Interactive terminal interface with real-time feedback
- Session management and conversation history
- Comprehensive tool calling with detailed usage guidelines`,
	Version: "1.0.0",
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	cobra.OnInitialize(initConfig)

	// Global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $HOME/.arien-ai/config.yaml)")
	rootCmd.PersistentFlags().Bool("verbose", false, "verbose output")
	rootCmd.PersistentFlags().Bool("debug", false, "debug mode")

	// Bind flags to viper
	viper.BindPFlag("verbose", rootCmd.PersistentFlags().Lookup("verbose"))
	viper.BindPFlag("debug", rootCmd.PersistentFlags().Lookup("debug"))

	// Add subcommands
	rootCmd.AddCommand(interactiveCmd)
	rootCmd.AddCommand(onboardingCmd)
	rootCmd.AddCommand(configCmd)
	rootCmd.AddCommand(sessionCmd)
	rootCmd.AddCommand(versionCmd)
}

// initConfig reads in config file and ENV variables.
func initConfig() {
	var err error
	
	// Initialize config manager
	configManager, err = config.NewManager()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error initializing config: %v\n", err)
		os.Exit(1)
	}

	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Use default config file from manager
		viper.SetConfigFile(configManager.GetConfigPath())
	}

	viper.AutomaticEnv() // read in environment variables that match

	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err == nil {
		if viper.GetBool("verbose") {
			fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
		}
	}
}

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Manage configuration settings",
	Long: `Manage Arien-AI configuration settings including LLM providers,
API keys, UI preferences, and security settings.`,
}

// sessionCmd represents the session command
var sessionCmd = &cobra.Command{
	Use:   "session",
	Short: "Manage conversation sessions",
	Long: `Manage conversation sessions including listing, loading,
saving, and deleting sessions.`,
}

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information",
	Long:  `Display version information for Arien-AI.`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("Arien-AI v%s\n", rootCmd.Version)
		fmt.Println("A powerful CLI terminal system with LLM integration")
		fmt.Println("Built with Go and Bubble Tea")
	},
}

func init() {
	// Config subcommands
	configCmd.AddCommand(&cobra.Command{
		Use:   "show",
		Short: "Show current configuration",
		Run: func(cmd *cobra.Command, args []string) {
			showConfig()
		},
	})

	configCmd.AddCommand(&cobra.Command{
		Use:   "set-provider [provider] [model]",
		Short: "Set LLM provider and model",
		Args:  cobra.ExactArgs(2),
		Run: func(cmd *cobra.Command, args []string) {
			setProvider(args[0], args[1])
		},
	})

	configCmd.AddCommand(&cobra.Command{
		Use:   "set-api-key [key]",
		Short: "Set API key for current provider",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			setAPIKey(args[0])
		},
	})

	configCmd.AddCommand(&cobra.Command{
		Use:   "set-base-url [url]",
		Short: "Set base URL for current provider",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			setBaseURL(args[0])
		},
	})

	configCmd.AddCommand(&cobra.Command{
		Use:   "reset",
		Short: "Reset configuration to defaults",
		Run: func(cmd *cobra.Command, args []string) {
			resetConfig()
		},
	})

	// Session subcommands
	sessionCmd.AddCommand(&cobra.Command{
		Use:   "list",
		Short: "List all sessions",
		Run: func(cmd *cobra.Command, args []string) {
			listSessions()
		},
	})

	sessionCmd.AddCommand(&cobra.Command{
		Use:   "load [session-id]",
		Short: "Load a specific session",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			loadSession(args[0])
		},
	})

	sessionCmd.AddCommand(&cobra.Command{
		Use:   "delete [session-id]",
		Short: "Delete a specific session",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			deleteSession(args[0])
		},
	})
}

// Configuration command implementations
func showConfig() {
	// Reload configuration to ensure we have the latest values
	if err := configManager.Reload(); err != nil {
		fmt.Fprintf(os.Stderr, "Warning: failed to reload config: %v\n", err)
	}

	config := configManager.Get()
	fmt.Printf("Current Configuration:\n")
	fmt.Printf("  LLM Provider: %s\n", config.LLM.Provider)
	fmt.Printf("  Model: %s\n", config.LLM.Model)
	fmt.Printf("  Base URL: %s\n", config.LLM.BaseURL)
	fmt.Printf("  Temperature: %.2f\n", config.LLM.Temperature)
	fmt.Printf("  Max Tokens: %d\n", config.LLM.MaxTokens)
	fmt.Printf("  Theme: %s\n", config.UI.Theme)
	fmt.Printf("  Sandbox Mode: %t\n", config.Security.SandboxMode)
	fmt.Printf("  Config File: %s\n", configManager.GetConfigPath())
}

func setProvider(provider, model string) {
	if err := configManager.SetLLMProvider(provider, model); err != nil {
		fmt.Fprintf(os.Stderr, "Error setting provider: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Provider set to %s with model %s\n", provider, model)
}

func setAPIKey(apiKey string) {
	if err := configManager.SetAPIKey(apiKey); err != nil {
		fmt.Fprintf(os.Stderr, "Error setting API key: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("API key updated successfully")
}

func setBaseURL(baseURL string) {
	if err := configManager.SetBaseURL(baseURL); err != nil {
		fmt.Fprintf(os.Stderr, "Error setting base URL: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Base URL updated to: %s\n", baseURL)
}

func resetConfig() {
	if err := configManager.Reset(); err != nil {
		fmt.Fprintf(os.Stderr, "Error resetting config: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("Configuration reset to defaults")
}

// Session command implementations (placeholder)
func listSessions() {
	fmt.Println("Session management not yet implemented")
	// TODO: Implement session listing
}

func loadSession(sessionID string) {
	fmt.Printf("Loading session %s (not yet implemented)\n", sessionID)
	// TODO: Implement session loading
}

func deleteSession(sessionID string) {
	fmt.Printf("Deleting session %s (not yet implemented)\n", sessionID)
	// TODO: Implement session deletion
}
