package executor

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
)

// SafetyChecker provides safety validation for commands and operations
type SafetyChecker struct {
	strictMode bool
}

// NewSafetyChecker creates a new safety checker
func NewSafetyChecker(strictMode bool) *SafetyChecker {
	return &SafetyChecker{
		strictMode: strictMode,
	}
}

// CheckCommand performs comprehensive safety checks on a command
func (s *SafetyChecker) CheckCommand(command string) error {
	if err := s.checkDestructivePatterns(command); err != nil {
		return err
	}

	if err := s.checkSystemModification(command); err != nil {
		return err
	}

	if err := s.checkNetworkSafety(command); err != nil {
		return err
	}

	if err := s.checkFileSystemSafety(command); err != nil {
		return err
	}

	return nil
}

// checkDestructivePatterns checks for potentially destructive command patterns
func (s *SafetyChecker) checkDestructivePatterns(command string) error {
	lowerCmd := strings.ToLower(command)

	// Extremely dangerous patterns
	dangerousPatterns := []string{
		"rm -rf /",
		"del /f /s /q c:\\",
		"format c:",
		"fdisk",
		"dd if=/dev/zero",
		":(){ :|:& };:",  // Fork bomb
		"shutdown -h now",
		"reboot",
		"halt",
		"poweroff",
		"init 0",
		"init 6",
	}

	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerCmd, pattern) {
			return fmt.Errorf("extremely dangerous command pattern detected: %s", pattern)
		}
	}

	// Potentially dangerous patterns (require confirmation)
	riskyPatterns := []string{
		"rm -rf",
		"rm -r",
		"del /s",
		"rmdir /s",
		"chmod 777",
		"chown root",
		"sudo rm",
		"sudo del",
		"mkfs",
		"parted",
		"crontab -r",
	}

	if s.strictMode {
		for _, pattern := range riskyPatterns {
			if strings.Contains(lowerCmd, pattern) {
				return fmt.Errorf("risky command pattern detected (strict mode): %s", pattern)
			}
		}
	}

	return nil
}

// checkSystemModification checks for system modification commands
func (s *SafetyChecker) checkSystemModification(command string) error {
	lowerCmd := strings.ToLower(command)

	systemPaths := []string{
		"/etc/",
		"/usr/",
		"/var/",
		"/boot/",
		"/sys/",
		"/proc/",
		"c:\\windows\\",
		"c:\\program files\\",
		"c:\\system32\\",
	}

	// Check if command modifies system paths
	modifyingCommands := []string{"rm", "del", "mv", "cp", "chmod", "chown"}
	
	for _, modCmd := range modifyingCommands {
		if strings.HasPrefix(lowerCmd, modCmd) {
			for _, sysPath := range systemPaths {
				if strings.Contains(lowerCmd, sysPath) {
					return fmt.Errorf("command attempts to modify system path: %s", sysPath)
				}
			}
		}
	}

	return nil
}

// checkNetworkSafety checks for potentially unsafe network operations
func (s *SafetyChecker) checkNetworkSafety(command string) error {
	lowerCmd := strings.ToLower(command)

	// Check for suspicious network commands
	suspiciousNetworkPatterns := []string{
		"curl | sh",
		"wget | sh",
		"curl | bash",
		"wget | bash",
		"nc -l",      // Netcat listener
		"ncat -l",
		"socat",
		"ssh-keygen", // Key generation should be explicit
	}

	for _, pattern := range suspiciousNetworkPatterns {
		if strings.Contains(lowerCmd, pattern) {
			return fmt.Errorf("suspicious network pattern detected: %s", pattern)
		}
	}

	return nil
}

// checkFileSystemSafety checks for file system safety
func (s *SafetyChecker) checkFileSystemSafety(command string) error {
	lowerCmd := strings.ToLower(command)

	// Check for operations on sensitive files
	sensitiveFiles := []string{
		"/etc/passwd",
		"/etc/shadow",
		"/etc/sudoers",
		"/etc/hosts",
		"/etc/fstab",
		"~/.ssh/",
		"~/.aws/",
		"~/.config/",
	}

	if runtime.GOOS == "windows" {
		sensitiveFiles = append(sensitiveFiles,
			"c:\\windows\\system32\\config\\",
			"c:\\windows\\system32\\drivers\\etc\\hosts",
		)
	}

	for _, file := range sensitiveFiles {
		if strings.Contains(lowerCmd, file) {
			return fmt.Errorf("command accesses sensitive file: %s", file)
		}
	}

	return nil
}

// CheckPath validates if a path is safe to access
func (s *SafetyChecker) CheckPath(path string) error {
	// Convert to absolute path
	absPath, err := filepath.Abs(path)
	if err != nil {
		return fmt.Errorf("invalid path: %w", err)
	}

	// Check for path traversal attempts
	if strings.Contains(path, "..") {
		return fmt.Errorf("path traversal detected: %s", path)
	}

	// Check if path exists and get info
	info, err := os.Stat(absPath)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("path does not exist: %s", absPath)
		}
		return fmt.Errorf("cannot access path: %w", err)
	}

	// Check permissions
	if info.Mode().Perm() == 0 {
		return fmt.Errorf("no permissions to access path: %s", absPath)
	}

	// Check for system directories
	systemDirs := []string{
		"/etc", "/usr", "/var", "/boot", "/sys", "/proc",
		"C:\\Windows", "C:\\Program Files", "C:\\System32",
	}

	for _, sysDir := range systemDirs {
		if strings.HasPrefix(strings.ToLower(absPath), strings.ToLower(sysDir)) {
			if s.strictMode {
				return fmt.Errorf("access to system directory not allowed in strict mode: %s", sysDir)
			}
		}
	}

	return nil
}

// CheckFileOperation validates file operations
func (s *SafetyChecker) CheckFileOperation(operation, path string) error {
	if err := s.CheckPath(path); err != nil {
		return err
	}

	lowerOp := strings.ToLower(operation)
	
	// Check for dangerous file operations
	if lowerOp == "delete" || lowerOp == "remove" {
		// Check if it's a critical file
		criticalFiles := []string{
			"config.yaml", "package.json", "go.mod", "Cargo.toml",
			"requirements.txt", "Dockerfile", "docker-compose.yml",
		}
		
		filename := filepath.Base(path)
		for _, critical := range criticalFiles {
			if strings.EqualFold(filename, critical) {
				return fmt.Errorf("deletion of critical file requires confirmation: %s", filename)
			}
		}
	}

	return nil
}

// IsPathSafe checks if a path is safe without returning an error
func (s *SafetyChecker) IsPathSafe(path string) bool {
	return s.CheckPath(path) == nil
}

// IsCommandSafe checks if a command is safe without returning an error
func (s *SafetyChecker) IsCommandSafe(command string) bool {
	return s.CheckCommand(command) == nil
}

// GetRiskLevel returns the risk level of a command
func (s *SafetyChecker) GetRiskLevel(command string) string {
	if err := s.checkDestructivePatterns(command); err != nil {
		return "HIGH"
	}
	
	if err := s.checkSystemModification(command); err != nil {
		return "MEDIUM"
	}
	
	if err := s.checkNetworkSafety(command); err != nil {
		return "MEDIUM"
	}
	
	if err := s.checkFileSystemSafety(command); err != nil {
		return "LOW"
	}
	
	return "SAFE"
}

// SuggestSaferAlternative suggests a safer alternative for risky commands
func (s *SafetyChecker) SuggestSaferAlternative(command string) string {
	lowerCmd := strings.ToLower(command)
	
	alternatives := map[string]string{
		"rm -rf":     "Use 'rm -r' with specific paths, or move to trash first",
		"chmod 777":  "Use more restrictive permissions like 755 or 644",
		"sudo rm":    "Use regular rm first, or move files to trash",
		"curl | sh":  "Download file first, inspect it, then execute",
		"wget | sh":  "Download file first, inspect it, then execute",
	}
	
	for pattern, alternative := range alternatives {
		if strings.Contains(lowerCmd, pattern) {
			return alternative
		}
	}
	
	return "No specific alternative suggested"
}

// SetStrictMode enables or disables strict mode
func (s *SafetyChecker) SetStrictMode(strict bool) {
	s.strictMode = strict
}

// IsStrictMode returns whether strict mode is enabled
func (s *SafetyChecker) IsStrictMode() bool {
	return s.strictMode
}
