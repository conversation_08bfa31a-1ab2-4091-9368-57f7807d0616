# Arien-AI

A modern, powerful CLI terminal system that integrates with LLM providers (Deepseek and Ollama) to execute shell commands intelligently and complete user tasks through function calling.

## 🚀 Features

- **LLM Integration**: Support for Deepseek and Ollama providers
- **Intelligent Command Execution**: AI-powered shell command execution with safety checks
- **Function Calling**: Advanced tool usage with detailed guidelines
- **Interactive Terminal**: Modern TUI built with Bubble Tea
- **Session Management**: Save and restore conversation sessions
- **Safety First**: Comprehensive command validation and sandboxing
- **Cross-Platform**: Works on Windows 11 WSL, macOS, and Linux

## 📦 Installation

### Quick Install (Recommended)

```bash
curl -fsSL https://raw.githubusercontent.com/your-username/arien-ai/main/scripts/install.sh | bash
```

### Manual Installation

1. Download the latest release for your platform from [Releases](https://github.com/your-username/arien-ai/releases)
2. Extract and move the binary to your PATH
3. Run `arien-ai onboard` to set up configuration

### Build from Source

```bash
git clone https://github.com/your-username/arien-ai.git
cd arien-ai
go build -o arien-ai
./arien-ai onboard
```

## 🎯 Quick Start

1. **Install Arien-AI** using one of the methods above
2. **Run onboarding** to configure your LLM provider:
   ```bash
   arien-ai onboard
   ```
3. **Start interactive mode**:
   ```bash
   arien-ai interactive
   ```

## 🔧 Configuration

### LLM Providers

#### Deepseek
- Get your API key from [Deepseek Platform](https://platform.deepseek.com)
- Models: `deepseek-chat`, `deepseek-reasoner`

#### Ollama
- Install [Ollama](https://ollama.ai) locally
- Pull models: `ollama pull llama3.3`
- Supported models: `llama3.3`, `deepseek-r1`, `qwen2.5`, `mistral`, `codellama`

### Configuration Commands

```bash
# Show current configuration
arien-ai config show

# Set LLM provider and model
arien-ai config set-provider deepseek deepseek-chat

# Set API key
arien-ai config set-api-key sk-your-api-key

# Reset to defaults
arien-ai config reset
```

## 💬 Usage

### Interactive Mode

Start the main terminal interface:

```bash
arien-ai interactive
```

### Slash Commands

Use slash commands for quick actions:

- `/model` - Change model
- `/provider` - Change provider
- `/session` - Session management
- `/history` - View message history
- `/quit` - Exit application

### Command Line Options

```bash
# Start with specific session
arien-ai interactive --session session-id

# Override provider/model
arien-ai interactive --provider ollama --model llama3.3

# Enable safe mode
arien-ai interactive --safe-mode

# Debug mode
arien-ai interactive --debug
```

## 🛠 Function Calling

Arien-AI supports advanced function calling with the following tools:

### Shell Command Execution

Execute shell commands with safety checks:

```
execute_shell_command({
  "command": "ls -la",
  "working_directory": "/home/<USER>",
  "timeout": 30,
  "capture_output": true,
  "require_confirmation": false
})
```

**Usage Guidelines:**
- ✅ Use for file operations, git commands, build tasks
- ✅ Use parallel execution for independent operations
- ✅ Use sequential execution for dependent commands
- ❌ Avoid destructive operations without confirmation
- ❌ Don't use for interactive commands

### File Operations

Read and write files safely:

```
read_file({
  "file_path": "config.yaml",
  "max_size": 10240,
  "encoding": "utf-8"
})

write_file({
  "file_path": "output.txt",
  "content": "Hello, World!",
  "backup": true,
  "mode": "write"
})
```

### System Information

Get system details:

```
get_system_info({
  "info_type": "basic",
  "include_sensitive": false
})
```

## 🔒 Security

### Safety Features

- **Command Validation**: Blocks dangerous commands
- **Sandbox Mode**: Restricts to whitelisted commands
- **User Confirmation**: Prompts for destructive operations
- **Timeout Protection**: Prevents long-running commands
- **Path Validation**: Prevents path traversal attacks

### Security Configuration

```yaml
security:
  sandbox_mode: false
  allowed_commands: ["ls", "git", "npm", "go", "python"]
  blocked_commands: ["rm -rf /", "format", "shutdown"]
  require_confirm: ["rm", "del", "sudo"]
  max_command_timeout: "5m"
```

## 📊 Session Management

### Session Commands

```bash
# List all sessions
arien-ai session list

# Load specific session
arien-ai session load session-id

# Delete session
arien-ai session delete session-id
```

### Session Features

- **Auto-save**: Automatically saves conversations
- **Context Window**: Maintains conversation context
- **Token Tracking**: Monitors API usage
- **Command History**: Tracks executed commands

## 🎨 Customization

### Themes

Available themes: `default`, `dark`, `light`

```bash
# Set theme in config
arien-ai config set ui.theme dark
```

### UI Configuration

```yaml
ui:
  theme: "default"
  show_timestamps: true
  show_token_count: true
  animation_speed: 100
  max_history_size: 1000
  auto_scroll: true
  confirm_commands: true
```

## 🔄 Updates

### Update Arien-AI

```bash
# Using install script
curl -fsSL https://raw.githubusercontent.com/your-username/arien-ai/main/scripts/install.sh | bash -s update

# Or manually
arien-ai version  # Check current version
# Download and install new version
```

### Uninstall

```bash
# Using install script
curl -fsSL https://raw.githubusercontent.com/your-username/arien-ai/main/scripts/install.sh | bash -s uninstall
```

## 🐛 Troubleshooting

### Common Issues

1. **Configuration not found**
   ```bash
   arien-ai onboard --force
   ```

2. **API connection failed**
   - Check API key: `arien-ai config show`
   - Verify internet connection
   - For Ollama: ensure it's running (`ollama serve`)

3. **Command execution failed**
   - Check permissions
   - Verify working directory
   - Review security settings

### Debug Mode

Enable debug mode for detailed logging:

```bash
arien-ai interactive --debug
```

## 📚 Documentation

- [Usage Guide](docs/USAGE.md)
- [Function Calling Tools](docs/TOOLS.md)
- [Examples](docs/EXAMPLES.md)
- [Architecture](ARCHITECTURE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Bubble Tea](https://github.com/charmbracelet/bubbletea) - TUI framework
- [Cobra](https://github.com/spf13/cobra) - CLI framework
- [Deepseek](https://www.deepseek.com) - AI provider
- [Ollama](https://ollama.ai) - Local AI runtime

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/arien-ai)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/arien-ai/issues)
- 📖 Docs: [Documentation](https://docs.arien-ai.com)
