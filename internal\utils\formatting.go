package utils

import (
	"fmt"
	"strings"
	"time"
	"unicode"
)

// FormatDuration formats a duration in a human-readable way
func FormatDuration(d time.Duration) string {
	if d < time.Second {
		return fmt.Sprintf("%dms", d.Milliseconds())
	}
	if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	}
	if d < time.Hour {
		return fmt.Sprintf("%.1fm", d.Minutes())
	}
	return fmt.Sprintf("%.1fh", d.Hours())
}

// FormatBytes formats bytes in a human-readable way
func FormatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// FormatTokenCount formats token count with appropriate units
func FormatTokenCount(tokens int) string {
	if tokens < 1000 {
		return fmt.Sprintf("%d tokens", tokens)
	}
	if tokens < 1000000 {
		return fmt.Sprintf("%.1fK tokens", float64(tokens)/1000)
	}
	return fmt.Sprintf("%.1fM tokens", float64(tokens)/1000000)
}

// TruncateString truncates a string to a maximum length with ellipsis
func TruncateString(s string, maxLength int) string {
	if len(s) <= maxLength {
		return s
	}
	if maxLength <= 3 {
		return "..."
	}
	return s[:maxLength-3] + "..."
}

// TruncateStringWords truncates a string at word boundaries
func TruncateStringWords(s string, maxLength int) string {
	if len(s) <= maxLength {
		return s
	}
	
	words := strings.Fields(s)
	if len(words) == 0 {
		return TruncateString(s, maxLength)
	}
	
	result := ""
	for _, word := range words {
		if len(result)+len(word)+1 > maxLength-3 {
			break
		}
		if result != "" {
			result += " "
		}
		result += word
	}
	
	if result == "" {
		return TruncateString(s, maxLength)
	}
	
	return result + "..."
}

// WrapText wraps text to a specified width
func WrapText(text string, width int) []string {
	if width <= 0 {
		return []string{text}
	}
	
	words := strings.Fields(text)
	if len(words) == 0 {
		return []string{}
	}
	
	var lines []string
	currentLine := ""
	
	for _, word := range words {
		if len(currentLine) == 0 {
			currentLine = word
		} else if len(currentLine)+1+len(word) <= width {
			currentLine += " " + word
		} else {
			lines = append(lines, currentLine)
			currentLine = word
		}
	}
	
	if currentLine != "" {
		lines = append(lines, currentLine)
	}
	
	return lines
}

// PadString pads a string to a specified width
func PadString(s string, width int, padChar rune) string {
	if len(s) >= width {
		return s
	}
	padding := strings.Repeat(string(padChar), width-len(s))
	return s + padding
}

// PadStringCenter centers a string within a specified width
func PadStringCenter(s string, width int, padChar rune) string {
	if len(s) >= width {
		return s
	}
	
	totalPadding := width - len(s)
	leftPadding := totalPadding / 2
	rightPadding := totalPadding - leftPadding
	
	return strings.Repeat(string(padChar), leftPadding) + s + strings.Repeat(string(padChar), rightPadding)
}

// IndentText indents each line of text with the specified prefix
func IndentText(text, prefix string) string {
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		if strings.TrimSpace(line) != "" {
			lines[i] = prefix + line
		}
	}
	return strings.Join(lines, "\n")
}

// RemoveExtraWhitespace removes extra whitespace from text
func RemoveExtraWhitespace(text string) string {
	// Replace multiple spaces with single space
	text = strings.Join(strings.Fields(text), " ")
	
	// Remove extra newlines
	lines := strings.Split(text, "\n")
	var cleanLines []string
	
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" || len(cleanLines) == 0 || cleanLines[len(cleanLines)-1] != "" {
			cleanLines = append(cleanLines, trimmed)
		}
	}
	
	return strings.Join(cleanLines, "\n")
}

// FormatList formats a list of items with bullets
func FormatList(items []string, bullet string) string {
	if bullet == "" {
		bullet = "• "
	}
	
	var formatted []string
	for _, item := range items {
		if strings.TrimSpace(item) != "" {
			formatted = append(formatted, bullet+item)
		}
	}
	
	return strings.Join(formatted, "\n")
}

// FormatNumberedList formats a list of items with numbers
func FormatNumberedList(items []string) string {
	var formatted []string
	counter := 1
	
	for _, item := range items {
		if strings.TrimSpace(item) != "" {
			formatted = append(formatted, fmt.Sprintf("%d. %s", counter, item))
			counter++
		}
	}
	
	return strings.Join(formatted, "\n")
}

// FormatTable formats data as a simple table
func FormatTable(headers []string, rows [][]string) string {
	if len(headers) == 0 || len(rows) == 0 {
		return ""
	}
	
	// Calculate column widths
	colWidths := make([]int, len(headers))
	for i, header := range headers {
		colWidths[i] = len(header)
	}
	
	for _, row := range rows {
		for i, cell := range row {
			if i < len(colWidths) && len(cell) > colWidths[i] {
				colWidths[i] = len(cell)
			}
		}
	}
	
	// Format header
	var result strings.Builder
	for i, header := range headers {
		if i > 0 {
			result.WriteString(" | ")
		}
		result.WriteString(PadString(header, colWidths[i], ' '))
	}
	result.WriteString("\n")
	
	// Add separator
	for i := range headers {
		if i > 0 {
			result.WriteString("-+-")
		}
		result.WriteString(strings.Repeat("-", colWidths[i]))
	}
	result.WriteString("\n")
	
	// Format rows
	for _, row := range rows {
		for i, cell := range row {
			if i > 0 {
				result.WriteString(" | ")
			}
			if i < len(colWidths) {
				result.WriteString(PadString(cell, colWidths[i], ' '))
			} else {
				result.WriteString(cell)
			}
		}
		result.WriteString("\n")
	}
	
	return result.String()
}

// FormatKeyValue formats key-value pairs
func FormatKeyValue(pairs map[string]string, separator string) string {
	if separator == "" {
		separator = ": "
	}
	
	var formatted []string
	for key, value := range pairs {
		formatted = append(formatted, key+separator+value)
	}
	
	return strings.Join(formatted, "\n")
}

// FormatProgress formats a progress bar
func FormatProgress(current, total int, width int) string {
	if total <= 0 || width <= 0 {
		return ""
	}
	
	percentage := float64(current) / float64(total)
	if percentage > 1.0 {
		percentage = 1.0
	}
	
	filled := int(percentage * float64(width))
	empty := width - filled
	
	bar := strings.Repeat("█", filled) + strings.Repeat("░", empty)
	return fmt.Sprintf("[%s] %d/%d (%.1f%%)", bar, current, total, percentage*100)
}

// CleanString removes non-printable characters from a string
func CleanString(s string) string {
	var result strings.Builder
	for _, r := range s {
		if unicode.IsPrint(r) || unicode.IsSpace(r) {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// EscapeString escapes special characters in a string
func EscapeString(s string) string {
	s = strings.ReplaceAll(s, "\\", "\\\\")
	s = strings.ReplaceAll(s, "\"", "\\\"")
	s = strings.ReplaceAll(s, "\n", "\\n")
	s = strings.ReplaceAll(s, "\r", "\\r")
	s = strings.ReplaceAll(s, "\t", "\\t")
	return s
}

// UnescapeString unescapes special characters in a string
func UnescapeString(s string) string {
	s = strings.ReplaceAll(s, "\\\\", "\\")
	s = strings.ReplaceAll(s, "\\\"", "\"")
	s = strings.ReplaceAll(s, "\\n", "\n")
	s = strings.ReplaceAll(s, "\\r", "\r")
	s = strings.ReplaceAll(s, "\\t", "\t")
	return s
}

// FormatJSON formats JSON string with proper indentation
func FormatJSON(jsonStr string) string {
	// This is a simple implementation
	// In production, you might want to use json.Indent
	var result strings.Builder
	indent := 0
	inString := false
	escaped := false
	
	for _, r := range jsonStr {
		if escaped {
			result.WriteRune(r)
			escaped = false
			continue
		}
		
		if r == '\\' && inString {
			result.WriteRune(r)
			escaped = true
			continue
		}
		
		if r == '"' {
			inString = !inString
			result.WriteRune(r)
			continue
		}
		
		if inString {
			result.WriteRune(r)
			continue
		}
		
		switch r {
		case '{', '[':
			result.WriteRune(r)
			result.WriteRune('\n')
			indent++
			result.WriteString(strings.Repeat("  ", indent))
		case '}', ']':
			result.WriteRune('\n')
			indent--
			result.WriteString(strings.Repeat("  ", indent))
			result.WriteRune(r)
		case ',':
			result.WriteRune(r)
			result.WriteRune('\n')
			result.WriteString(strings.Repeat("  ", indent))
		case ':':
			result.WriteRune(r)
			result.WriteRune(' ')
		default:
			if !unicode.IsSpace(r) {
				result.WriteRune(r)
			}
		}
	}
	
	return result.String()
}

// Pluralize returns the singular or plural form based on count
func Pluralize(count int, singular, plural string) string {
	if count == 1 {
		return singular
	}
	return plural
}

// FormatCount formats a count with the appropriate singular/plural form
func FormatCount(count int, singular, plural string) string {
	return fmt.Sprintf("%d %s", count, Pluralize(count, singular, plural))
}
