package config

import (
	"fmt"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// ProviderConfig represents configuration for a specific LLM provider
type ProviderConfig struct {
	Name         string            `yaml:"name" json:"name"`
	DisplayName  string            `yaml:"display_name" json:"display_name"`
	BaseURL      string            `yaml:"base_url" json:"base_url"`
	APIKeyEnv    string            `yaml:"api_key_env" json:"api_key_env"`
	Models       []ModelConfig     `yaml:"models" json:"models"`
	Headers      map[string]string `yaml:"headers" json:"headers"`
	Timeout      time.Duration     `yaml:"timeout" json:"timeout"`
	MaxTokens    int               `yaml:"max_tokens" json:"max_tokens"`
	Temperature  float64           `yaml:"temperature" json:"temperature"`
	RequiresAuth bool              `yaml:"requires_auth" json:"requires_auth"`
}

// ModelConfig represents configuration for a specific model
type ModelConfig struct {
	Name         string  `yaml:"name" json:"name"`
	DisplayName  string  `yaml:"display_name" json:"display_name"`
	Description  string  `yaml:"description" json:"description"`
	MaxTokens    int     `yaml:"max_tokens" json:"max_tokens"`
	Temperature  float64 `yaml:"temperature" json:"temperature"`
	ContextSize  int     `yaml:"context_size" json:"context_size"`
	CostPer1K    float64 `yaml:"cost_per_1k" json:"cost_per_1k"`
	Capabilities []string `yaml:"capabilities" json:"capabilities"`
}

// GetSupportedProviders returns all supported LLM providers
func GetSupportedProviders() map[string]ProviderConfig {
	return map[string]ProviderConfig{
		"deepseek": {
			Name:         "deepseek",
			DisplayName:  "Deepseek",
			BaseURL:      "https://api.deepseek.com/v1",
			APIKeyEnv:    "DEEPSEEK_API_KEY",
			RequiresAuth: true,
			Timeout:      30 * time.Second,
			MaxTokens:    4096,
			Temperature:  0.7,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Models: []ModelConfig{
				{
					Name:        "deepseek-chat",
					DisplayName: "Deepseek Chat",
					Description: "General purpose conversational model",
					MaxTokens:   4096,
					Temperature: 0.7,
					ContextSize: 32768,
					CostPer1K:   0.14,
					Capabilities: []string{
						"chat",
						"function_calling",
						"code_generation",
						"reasoning",
					},
				},
				{
					Name:        "deepseek-reasoner",
					DisplayName: "Deepseek Reasoner",
					Description: "Advanced reasoning and problem-solving model",
					MaxTokens:   8192,
					Temperature: 0.5,
					ContextSize: 65536,
					CostPer1K:   0.55,
					Capabilities: []string{
						"chat",
						"function_calling",
						"advanced_reasoning",
						"code_generation",
						"mathematical_reasoning",
					},
				},
			},
		},
		"ollama": {
			Name:         "ollama",
			DisplayName:  "Ollama",
			BaseURL:      "http://localhost:11434",
			RequiresAuth: false,
			Timeout:      60 * time.Second,
			MaxTokens:    4096,
			Temperature:  0.7,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Models: []ModelConfig{
				{
					Name:        "llama3.3",
					DisplayName: "Llama 3.3",
					Description: "Meta's latest Llama model",
					MaxTokens:   4096,
					Temperature: 0.7,
					ContextSize: 8192,
					CostPer1K:   0.0, // Local model
					Capabilities: []string{
						"chat",
						"function_calling",
						"code_generation",
					},
				},
				{
					Name:        "deepseek-r1",
					DisplayName: "Deepseek R1",
					Description: "Deepseek reasoning model via Ollama",
					MaxTokens:   8192,
					Temperature: 0.5,
					ContextSize: 32768,
					CostPer1K:   0.0, // Local model
					Capabilities: []string{
						"chat",
						"function_calling",
						"advanced_reasoning",
						"code_generation",
					},
				},
				{
					Name:        "qwen2.5",
					DisplayName: "Qwen 2.5",
					Description: "Alibaba's Qwen model",
					MaxTokens:   4096,
					Temperature: 0.7,
					ContextSize: 32768,
					CostPer1K:   0.0, // Local model
					Capabilities: []string{
						"chat",
						"function_calling",
						"code_generation",
						"multilingual",
					},
				},
				{
					Name:        "mistral",
					DisplayName: "Mistral",
					Description: "Mistral AI model",
					MaxTokens:   4096,
					Temperature: 0.7,
					ContextSize: 8192,
					CostPer1K:   0.0, // Local model
					Capabilities: []string{
						"chat",
						"function_calling",
						"code_generation",
					},
				},
				{
					Name:        "codellama",
					DisplayName: "Code Llama",
					Description: "Specialized code generation model",
					MaxTokens:   4096,
					Temperature: 0.3,
					ContextSize: 16384,
					CostPer1K:   0.0, // Local model
					Capabilities: []string{
						"code_generation",
						"code_completion",
						"code_explanation",
						"debugging",
					},
				},
			},
		},
	}
}

// GetProviderConfig returns configuration for a specific provider
func GetProviderConfig(provider string) (ProviderConfig, error) {
	providers := GetSupportedProviders()
	config, exists := providers[strings.ToLower(provider)]
	if !exists {
		return ProviderConfig{}, fmt.Errorf("unsupported provider: %s", provider)
	}
	return config, nil
}

// GetModelConfig returns configuration for a specific model within a provider
func GetModelConfig(provider, model string) (ModelConfig, error) {
	providerConfig, err := GetProviderConfig(provider)
	if err != nil {
		return ModelConfig{}, err
	}

	for _, modelConfig := range providerConfig.Models {
		if modelConfig.Name == model {
			return modelConfig, nil
		}
	}

	return ModelConfig{}, fmt.Errorf("model %s not found for provider %s", model, provider)
}

// ValidateProviderModel validates if a provider and model combination is supported
func ValidateProviderModel(provider, model string) error {
	_, err := GetModelConfig(provider, model)
	return err
}

// GetAvailableModels returns all available models for a provider
func GetAvailableModels(provider string) ([]ModelConfig, error) {
	providerConfig, err := GetProviderConfig(provider)
	if err != nil {
		return nil, err
	}
	return providerConfig.Models, nil
}

// GetAllProviders returns a list of all supported provider names
func GetAllProviders() []string {
	providers := GetSupportedProviders()
	names := make([]string, 0, len(providers))
	for name := range providers {
		names = append(names, name)
	}
	return names
}

// CreateLLMConfigFromProvider creates an LLMConfig from provider and model
func CreateLLMConfigFromProvider(provider, model string) (types.LLMConfig, error) {
	providerConfig, err := GetProviderConfig(provider)
	if err != nil {
		return types.LLMConfig{}, err
	}

	modelConfig, err := GetModelConfig(provider, model)
	if err != nil {
		return types.LLMConfig{}, err
	}

	return types.LLMConfig{
		Provider:    provider,
		Model:       model,
		BaseURL:     providerConfig.BaseURL,
		Temperature: float32(modelConfig.Temperature),
		MaxTokens:   modelConfig.MaxTokens,
		Timeout:     providerConfig.Timeout,
		Headers:     providerConfig.Headers,
	}, nil
}

// HasCapability checks if a model has a specific capability
func HasCapability(provider, model, capability string) bool {
	modelConfig, err := GetModelConfig(provider, model)
	if err != nil {
		return false
	}

	for _, cap := range modelConfig.Capabilities {
		if cap == capability {
			return true
		}
	}
	return false
}

// GetModelsByCapability returns models that have a specific capability
func GetModelsByCapability(capability string) []ModelConfig {
	var models []ModelConfig
	providers := GetSupportedProviders()

	for _, provider := range providers {
		for _, model := range provider.Models {
			for _, cap := range model.Capabilities {
				if cap == capability {
					models = append(models, model)
					break
				}
			}
		}
	}

	return models
}
