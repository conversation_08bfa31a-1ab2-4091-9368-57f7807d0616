package llm

import (
	"encoding/json"
	"fmt"
)

// GetShellTool returns the shell command execution tool definition
func GetShellTool() Tool {
	return Tool{
		Type: "function",
		Function: Function{
			Name: "execute_shell_command",
			Description: `Execute shell commands with safety checks and output capture.

USAGE GUIDELINES:
- Use for file system operations, process management, system information gathering
- Use for development tasks (build, test, deploy)
- Always specify working_directory when path-dependent operations are needed
- Set require_confirmation=true for destructive operations
- Use appropriate timeout values (default: 30 seconds)

WHEN TO USE:
- File operations: ls, dir, cat, head, tail, find, grep
- Git operations: git status, git log, git diff, git add, git commit
- Build operations: npm install, go build, make, cargo build
- System info: ps, top, df, free, uname
- Development: running tests, starting servers, checking logs

WHEN NOT TO USE:
- Destructive operations without confirmation (rm -rf, format, etc.)
- Commands requiring interactive input (interactive editors, prompts)
- Long-running background processes (use with caution and proper timeout)
- Commands that modify system configuration without user consent

PARALLEL vs SEQUENTIAL EXECUTION:
- PARALLEL: Use for independent operations that don't depend on each other
  Example: checking multiple log files, running tests in different directories
- SEQUENTIAL: Use when commands depend on previous results or modify shared state
  Example: cd to directory, then run command; git add, then git commit

SAFETY FEATURES:
- Command validation against blocklist
- Working directory validation
- Timeout protection
- Output size limits
- Permission checks`,
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"command": map[string]interface{}{
						"type":        "string",
						"description": "The shell command to execute. Use proper shell syntax for the target OS.",
					},
					"working_directory": map[string]interface{}{
						"type":        "string",
						"description": "Directory to execute the command in. Use absolute or relative paths.",
						"default":     ".",
					},
					"timeout": map[string]interface{}{
						"type":        "integer",
						"description": "Command timeout in seconds. Default: 30, Max: 300",
						"default":     30,
						"minimum":     1,
						"maximum":     300,
					},
					"capture_output": map[string]interface{}{
						"type":        "boolean",
						"description": "Whether to capture and return command output. Default: true",
						"default":     true,
					},
					"require_confirmation": map[string]interface{}{
						"type":        "boolean",
						"description": "Whether to ask user confirmation before execution. Default: false",
						"default":     false,
					},
					"environment": map[string]interface{}{
						"type":        "object",
						"description": "Additional environment variables to set for the command",
						"additionalProperties": map[string]interface{}{
							"type": "string",
						},
					},
				},
				"required": []string{"command"},
			},
		},
	}
}

// GetFileReadTool returns the file reading tool definition
func GetFileReadTool() Tool {
	return Tool{
		Type: "function",
		Function: Function{
			Name: "read_file",
			Description: `Read the contents of a file with safety checks.

USAGE GUIDELINES:
- Use for reading configuration files, logs, source code, documentation
- Specify encoding when dealing with non-UTF8 files
- Use max_size to prevent reading extremely large files
- Set line_range to read specific sections of large files

WHEN TO USE:
- Reading configuration files for analysis
- Examining log files for debugging
- Reading source code for understanding
- Checking file contents before modification

WHEN NOT TO USE:
- Reading binary files (images, executables, etc.)
- Reading extremely large files without size limits
- Reading files that require special permissions

EXAMPLES:
- Read config: {"file_path": "config.yaml", "max_size": 10240}
- Read logs: {"file_path": "/var/log/app.log", "line_range": [100, 200]}
- Read source: {"file_path": "src/main.go", "encoding": "utf-8"}`,
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "Path to the file to read (absolute or relative)",
					},
					"encoding": map[string]interface{}{
						"type":        "string",
						"description": "File encoding (default: utf-8)",
						"default":     "utf-8",
						"enum":        []string{"utf-8", "ascii", "latin-1"},
					},
					"max_size": map[string]interface{}{
						"type":        "integer",
						"description": "Maximum file size to read in bytes (default: 1MB)",
						"default":     1048576,
						"minimum":     1,
						"maximum":     10485760,
					},
					"line_range": map[string]interface{}{
						"type":        "array",
						"description": "Range of lines to read [start, end] (1-indexed, inclusive)",
						"items":       map[string]interface{}{"type": "integer"},
						"minItems":    2,
						"maxItems":    2,
					},
				},
				"required": []string{"file_path"},
			},
		},
	}
}

// GetFileWriteTool returns the file writing tool definition
func GetFileWriteTool() Tool {
	return Tool{
		Type: "function",
		Function: Function{
			Name: "write_file",
			Description: `Write content to a file with safety checks and backup options.

USAGE GUIDELINES:
- Always create backups for existing files when backup=true
- Use appropriate file permissions (default: 644)
- Specify encoding for non-UTF8 content
- Use append mode to add to existing files

WHEN TO USE:
- Creating new configuration files
- Writing generated code or scripts
- Saving processed data or reports
- Creating documentation files

WHEN NOT TO USE:
- Writing to system files without proper permissions
- Overwriting critical files without backups
- Writing binary content (use appropriate tools instead)

SAFETY FEATURES:
- Automatic backup creation
- Permission validation
- Path traversal protection
- Content size limits

EXAMPLES:
- New file: {"file_path": "config.yaml", "content": "...", "backup": false}
- Update file: {"file_path": "app.conf", "content": "...", "backup": true}
- Append log: {"file_path": "app.log", "content": "...", "mode": "append"}`,
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"file_path": map[string]interface{}{
						"type":        "string",
						"description": "Path to the file to write (absolute or relative)",
					},
					"content": map[string]interface{}{
						"type":        "string",
						"description": "Content to write to the file",
					},
					"mode": map[string]interface{}{
						"type":        "string",
						"description": "Write mode: 'write' (overwrite) or 'append'",
						"default":     "write",
						"enum":        []string{"write", "append"},
					},
					"encoding": map[string]interface{}{
						"type":        "string",
						"description": "File encoding (default: utf-8)",
						"default":     "utf-8",
						"enum":        []string{"utf-8", "ascii", "latin-1"},
					},
					"backup": map[string]interface{}{
						"type":        "boolean",
						"description": "Create backup of existing file before writing",
						"default":     true,
					},
					"permissions": map[string]interface{}{
						"type":        "string",
						"description": "File permissions in octal format (default: 644)",
						"default":     "644",
						"pattern":     "^[0-7]{3}$",
					},
				},
				"required": []string{"file_path", "content"},
			},
		},
	}
}

// GetSystemInfoTool returns the system information tool definition
func GetSystemInfoTool() Tool {
	return Tool{
		Type: "function",
		Function: Function{
			Name: "get_system_info",
			Description: `Get system information including OS, hardware, and environment details.

USAGE GUIDELINES:
- Use to understand the current system environment
- Helpful for debugging environment-specific issues
- Use specific info_type to get targeted information
- Combine with other tools for comprehensive system analysis

WHEN TO USE:
- Debugging environment issues
- Checking system requirements
- Understanding available resources
- Gathering context for command execution

INFORMATION TYPES:
- 'basic': OS, architecture, hostname
- 'hardware': CPU, memory, disk space
- 'environment': Environment variables, PATH
- 'network': Network interfaces, connectivity
- 'processes': Running processes and services
- 'all': Complete system information

EXAMPLES:
- Basic info: {"info_type": "basic"}
- Check resources: {"info_type": "hardware"}
- Debug env: {"info_type": "environment"}`,
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"info_type": map[string]interface{}{
						"type":        "string",
						"description": "Type of system information to retrieve",
						"default":     "basic",
						"enum":        []string{"basic", "hardware", "environment", "network", "processes", "all"},
					},
					"include_sensitive": map[string]interface{}{
						"type":        "boolean",
						"description": "Include potentially sensitive information (env vars, etc.)",
						"default":     false,
					},
				},
				"required": []string{},
			},
		},
	}
}

// GetAllTools returns all available tools
func GetAllTools() []Tool {
	return []Tool{
		GetShellTool(),
		GetFileReadTool(),
		GetFileWriteTool(),
		GetSystemInfoTool(),
	}
}

// GetToolByName returns a tool by its name
func GetToolByName(name string) (Tool, bool) {
	tools := GetAllTools()
	for _, tool := range tools {
		if tool.Function.Name == name {
			return tool, true
		}
	}
	return Tool{}, false
}

// ValidateToolCall validates a tool call request
func ValidateToolCall(toolName string, arguments json.RawMessage) error {
	tool, exists := GetToolByName(toolName)
	if !exists {
		return fmt.Errorf("unknown tool: %s", toolName)
	}

	// Parse arguments
	var args map[string]interface{}
	if err := json.Unmarshal(arguments, &args); err != nil {
		return fmt.Errorf("invalid arguments JSON: %w", err)
	}

	// Validate required parameters
	params := tool.Function.Parameters.(map[string]interface{})
	properties := params["properties"].(map[string]interface{})
	required := params["required"].([]string)

	for _, reqParam := range required {
		if _, exists := args[reqParam]; !exists {
			return fmt.Errorf("missing required parameter: %s", reqParam)
		}
	}

	// Validate parameter types (basic validation)
	for paramName, paramValue := range args {
		if propDef, exists := properties[paramName]; exists {
			propMap := propDef.(map[string]interface{})
			expectedType := propMap["type"].(string)
			
			if !validateParameterType(paramValue, expectedType) {
				return fmt.Errorf("parameter %s has invalid type, expected %s", paramName, expectedType)
			}
		}
	}

	return nil
}

// validateParameterType validates if a parameter value matches the expected type
func validateParameterType(value interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "integer":
		_, ok := value.(float64) // JSON numbers are float64
		return ok
	case "boolean":
		_, ok := value.(bool)
		return ok
	case "array":
		_, ok := value.([]interface{})
		return ok
	case "object":
		_, ok := value.(map[string]interface{})
		return ok
	default:
		return true // Unknown type, assume valid
	}
}
