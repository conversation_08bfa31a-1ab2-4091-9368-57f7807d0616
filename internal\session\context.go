package session

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// ContextManager manages conversation context and message prioritization
type ContextManager struct {
	maxContextSize int
	tokenLimit     int
	priorityRules  []PriorityRule
}

// PriorityRule defines how to prioritize messages for context inclusion
type PriorityRule struct {
	MessageType types.MessageType
	Priority    int
	MaxAge      time.Duration
	MaxCount    int
}

// ContextWindow represents a window of messages for LLM context
type ContextWindow struct {
	Messages     []*types.Message
	TokenCount   int
	MessageCount int
	TimeSpan     time.Duration
}

// NewContextManager creates a new context manager
func NewContextManager(maxContextSize, tokenLimit int) *ContextManager {
	return &ContextManager{
		maxContextSize: maxContextSize,
		tokenLimit:     tokenLimit,
		priorityRules: []PriorityRule{
			{
				MessageType: types.MessageTypeSystem,
				Priority:    100,
				MaxAge:      24 * time.Hour,
				MaxCount:    5,
			},
			{
				MessageType: types.MessageTypeUser,
				Priority:    90,
				MaxAge:      2 * time.Hour,
				MaxCount:    20,
			},
			{
				MessageType: types.MessageTypeAssistant,
				Priority:    85,
				MaxAge:      2 * time.Hour,
				MaxCount:    20,
			},
			{
				MessageType: types.MessageTypeFunction,
				Priority:    80,
				MaxAge:      1 * time.Hour,
				MaxCount:    10,
			},
			{
				MessageType: types.MessageTypeTool,
				Priority:    75,
				MaxAge:      1 * time.Hour,
				MaxCount:    15,
			},
		},
	}
}

// BuildContext builds an optimal context window from session messages
func (cm *ContextManager) BuildContext(session *types.Session) (*ContextWindow, error) {
	if len(session.Messages) == 0 {
		return &ContextWindow{
			Messages:     []*types.Message{},
			TokenCount:   0,
			MessageCount: 0,
			TimeSpan:     0,
		}, nil
	}

	// Get prioritized messages
	prioritizedMessages := cm.prioritizeMessages(session.Messages)

	// Build context window within limits
	contextWindow := cm.buildContextWindow(prioritizedMessages)

	return contextWindow, nil
}

// prioritizeMessages sorts and filters messages based on priority rules
func (cm *ContextManager) prioritizeMessages(messages []*types.Message) []*types.Message {
	now := time.Now()
	var prioritizedMessages []*types.Message

	// Group messages by type
	messagesByType := make(map[types.MessageType][]*types.Message)
	for _, msg := range messages {
		messagesByType[msg.Type] = append(messagesByType[msg.Type], msg)
	}

	// Apply priority rules for each message type
	for _, rule := range cm.priorityRules {
		typeMessages := messagesByType[rule.MessageType]
		if len(typeMessages) == 0 {
			continue
		}

		// Filter by age
		var recentMessages []*types.Message
		for _, msg := range typeMessages {
			if now.Sub(msg.Timestamp) <= rule.MaxAge {
				recentMessages = append(recentMessages, msg)
			}
		}

		// Sort by timestamp (newest first)
		sort.Slice(recentMessages, func(i, j int) bool {
			return recentMessages[i].Timestamp.After(recentMessages[j].Timestamp)
		})

		// Limit count
		if len(recentMessages) > rule.MaxCount {
			recentMessages = recentMessages[:rule.MaxCount]
		}

		prioritizedMessages = append(prioritizedMessages, recentMessages...)
	}

	// Sort final list by timestamp to maintain conversation order
	sort.Slice(prioritizedMessages, func(i, j int) bool {
		return prioritizedMessages[i].Timestamp.Before(prioritizedMessages[j].Timestamp)
	})

	return prioritizedMessages
}

// buildContextWindow creates a context window within token and size limits
func (cm *ContextManager) buildContextWindow(messages []*types.Message) *ContextWindow {
	window := &ContextWindow{
		Messages:     []*types.Message{},
		TokenCount:   0,
		MessageCount: 0,
	}

	if len(messages) == 0 {
		return window
	}

	// Start from the most recent messages and work backwards
	for i := len(messages) - 1; i >= 0; i-- {
		msg := messages[i]
		msgTokens := cm.estimateTokens(msg)

		// Check if adding this message would exceed limits
		if window.TokenCount+msgTokens > cm.tokenLimit ||
			window.MessageCount >= cm.maxContextSize {
			break
		}

		// Add message to the beginning of the window
		window.Messages = append([]*types.Message{msg}, window.Messages...)
		window.TokenCount += msgTokens
		window.MessageCount++
	}

	// Calculate time span
	if len(window.Messages) > 0 {
		oldest := window.Messages[0].Timestamp
		newest := window.Messages[len(window.Messages)-1].Timestamp
		window.TimeSpan = newest.Sub(oldest)
	}

	return window
}

// estimateTokens estimates the token count for a message
func (cm *ContextManager) estimateTokens(msg *types.Message) int {
	// Simple estimation: ~4 characters per token
	content := msg.Content
	if msg.FunctionCall != nil {
		content += msg.FunctionCall.Name + string(msg.FunctionCall.Arguments)
	}
	if msg.ToolCalls != nil {
		for _, tool := range msg.ToolCalls {
			content += tool.Function.Name + string(tool.Function.Arguments)
		}
	}

	return len(content) / 4
}

// GetRecentMessages returns the most recent messages up to a limit
func (cm *ContextManager) GetRecentMessages(messages []*types.Message, limit int) []*types.Message {
	if len(messages) <= limit {
		return messages
	}
	
	return messages[len(messages)-limit:]
}

// GetMessagesByType returns messages of a specific type
func (cm *ContextManager) GetMessagesByType(messages []*types.Message, msgType types.MessageType) []*types.Message {
	var filtered []*types.Message
	for _, msg := range messages {
		if msg.Type == msgType {
			filtered = append(filtered, msg)
		}
	}
	return filtered
}

// GetMessagesByTimeRange returns messages within a time range
func (cm *ContextManager) GetMessagesByTimeRange(messages []*types.Message, start, end time.Time) []*types.Message {
	var filtered []*types.Message
	for _, msg := range messages {
		if msg.Timestamp.After(start) && msg.Timestamp.Before(end) {
			filtered = append(filtered, msg)
		}
	}
	return filtered
}

// SummarizeContext creates a summary of the context window
func (cm *ContextManager) SummarizeContext(window *ContextWindow) string {
	if window.MessageCount == 0 {
		return "Empty context"
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("Context: %d messages, %d tokens, %v timespan\n",
		window.MessageCount, window.TokenCount, window.TimeSpan.Round(time.Minute)))

	// Count messages by type
	typeCounts := make(map[types.MessageType]int)
	for _, msg := range window.Messages {
		typeCounts[msg.Type]++
	}

	summary.WriteString("Message types: ")
	var typeStrings []string
	for msgType, count := range typeCounts {
		typeStrings = append(typeStrings, fmt.Sprintf("%s(%d)", msgType, count))
	}
	summary.WriteString(strings.Join(typeStrings, ", "))

	return summary.String()
}

// OptimizeContext optimizes the context window for better performance
func (cm *ContextManager) OptimizeContext(window *ContextWindow) *ContextWindow {
	if window.MessageCount <= cm.maxContextSize/2 {
		return window // No optimization needed
	}

	// Remove redundant system messages (keep only the latest)
	optimized := &ContextWindow{
		Messages:   []*types.Message{},
		TokenCount: 0,
	}

	var lastSystemMsg *types.Message
	for _, msg := range window.Messages {
		if msg.Type == types.MessageTypeSystem {
			lastSystemMsg = msg
		} else {
			// Add the last system message if we haven't added it yet
			if lastSystemMsg != nil {
				optimized.Messages = append(optimized.Messages, lastSystemMsg)
				optimized.TokenCount += cm.estimateTokens(lastSystemMsg)
				lastSystemMsg = nil
			}
			optimized.Messages = append(optimized.Messages, msg)
			optimized.TokenCount += cm.estimateTokens(msg)
		}
	}

	// Add the final system message if it exists
	if lastSystemMsg != nil {
		optimized.Messages = append(optimized.Messages, lastSystemMsg)
		optimized.TokenCount += cm.estimateTokens(lastSystemMsg)
	}

	optimized.MessageCount = len(optimized.Messages)
	
	// Recalculate time span
	if len(optimized.Messages) > 0 {
		oldest := optimized.Messages[0].Timestamp
		newest := optimized.Messages[len(optimized.Messages)-1].Timestamp
		optimized.TimeSpan = newest.Sub(oldest)
	}

	return optimized
}

// UpdatePriorityRules updates the priority rules for context management
func (cm *ContextManager) UpdatePriorityRules(rules []PriorityRule) {
	cm.priorityRules = rules
}

// GetContextStats returns statistics about context usage
func (cm *ContextManager) GetContextStats(session *types.Session) *types.ContextStats {
	stats := &types.ContextStats{
		TotalMessages:   len(session.Messages),
		MaxContextSize:  cm.maxContextSize,
		TokenLimit:      cm.tokenLimit,
		MessagesByType:  make(map[types.MessageType]int),
	}

	for _, msg := range session.Messages {
		stats.MessagesByType[msg.Type]++
		stats.TotalTokens += cm.estimateTokens(msg)
	}

	// Calculate context window
	window, _ := cm.BuildContext(session)
	stats.ContextMessages = window.MessageCount
	stats.ContextTokens = window.TokenCount
	stats.ContextUtilization = float64(window.MessageCount) / float64(cm.maxContextSize)

	return stats
}
