package components

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/ui/styles"
	"arien-ai/internal/utils"
	"arien-ai/pkg/types"
)

// StatusBarComponent represents the status bar UI component
type StatusBarComponent struct {
	theme   *styles.Theme
	width   int
	session *types.Session
	config  *types.Config
	status  StatusInfo
}

// StatusInfo contains information to display in the status bar
type StatusInfo struct {
	Mode           string
	Message        string
	Progress       *ProgressInfo
	ConnectionInfo *ConnectionInfo
	SessionInfo    *SessionInfo
	SystemInfo     *SystemInfo
}

// ProgressInfo contains progress information
type ProgressInfo struct {
	Current int
	Total   int
	Label   string
}

// ConnectionInfo contains connection status information
type ConnectionInfo struct {
	Provider  string
	Model     string
	Connected bool
	LastPing  time.Time
}

// SessionInfo contains session information
type SessionInfo struct {
	MessageCount int
	TokenUsage   types.TokenUsage
	Duration     time.Duration
}

// SystemInfo contains system information
type SystemInfo struct {
	CPUUsage    float64
	MemoryUsage float64
	DiskUsage   float64
}

// NewStatusBarComponent creates a new status bar component
func NewStatusBarComponent(theme *styles.Theme, session *types.Session, config *types.Config) *StatusBarComponent {
	return &StatusBarComponent{
		theme:   theme,
		session: session,
		config:  config,
	}
}

// SetWidth sets the width of the status bar component
func (s *StatusBarComponent) SetWidth(width int) {
	s.width = width
}

// Update updates the status bar with new information
func (s *StatusBarComponent) Update(session *types.Session, config *types.Config) {
	s.session = session
	s.config = config
}

// SetStatus sets the status information
func (s *StatusBarComponent) SetStatus(status StatusInfo) {
	s.status = status
}

// SetMode sets the current mode
func (s *StatusBarComponent) SetMode(mode string) {
	s.status.Mode = mode
}

// SetMessage sets the status message
func (s *StatusBarComponent) SetMessage(message string) {
	s.status.Message = message
}

// SetProgress sets progress information
func (s *StatusBarComponent) SetProgress(current, total int, label string) {
	s.status.Progress = &ProgressInfo{
		Current: current,
		Total:   total,
		Label:   label,
	}
}

// ClearProgress clears progress information
func (s *StatusBarComponent) ClearProgress() {
	s.status.Progress = nil
}

// Render renders the status bar component
func (s *StatusBarComponent) Render() string {
	if s.width <= 0 {
		return ""
	}

	// Create status sections
	leftSection := s.renderLeftSection()
	centerSection := s.renderCenterSection()
	rightSection := s.renderRightSection()

	// Calculate available space
	leftWidth := lipgloss.Width(leftSection)
	rightWidth := lipgloss.Width(rightSection)
	centerWidth := s.width - leftWidth - rightWidth - 4 // 4 for spacing

	if centerWidth < 0 {
		centerWidth = 0
	}

	// Truncate center section if needed
	if lipgloss.Width(centerSection) > centerWidth {
		centerSection = utils.TruncateString(centerSection, centerWidth)
	}

	// Combine sections
	content := leftSection + strings.Repeat(" ", 2) + 
		utils.PadStringCenter(centerSection, centerWidth, ' ') + 
		strings.Repeat(" ", 2) + rightSection

	// Apply status bar style
	return s.theme.Styles.StatusContainer.Width(s.width).Render(content)
}

// renderLeftSection renders the left section of the status bar
func (s *StatusBarComponent) renderLeftSection() string {
	var parts []string

	// Mode indicator
	if s.status.Mode != "" {
		modeStyle := s.theme.Styles.TextPrimary
		parts = append(parts, modeStyle.Render(s.status.Mode))
	}

	// Status message
	if s.status.Message != "" {
		messageStyle := s.theme.Styles.TextSecondary
		parts = append(parts, messageStyle.Render(s.status.Message))
	}

	return strings.Join(parts, " | ")
}

// renderCenterSection renders the center section of the status bar
func (s *StatusBarComponent) renderCenterSection() string {
	// Show progress if available
	if s.status.Progress != nil {
		return s.renderProgress()
	}

	// Show session info
	if s.session != nil {
		return s.renderSessionInfo()
	}

	return ""
}

// renderRightSection renders the right section of the status bar
func (s *StatusBarComponent) renderRightSection() string {
	var parts []string

	// Connection status
	if s.status.ConnectionInfo != nil {
		parts = append(parts, s.renderConnectionStatus())
	} else if s.config != nil {
		// Default connection info
		connText := fmt.Sprintf("%s/%s", s.config.LLM.Provider, s.config.LLM.Model)
		parts = append(parts, s.theme.Styles.TextMuted.Render(connText))
	}

	// System info
	if s.status.SystemInfo != nil {
		parts = append(parts, s.renderSystemInfo())
	}

	// Current time
	timeText := time.Now().Format("15:04")
	parts = append(parts, s.theme.Styles.TextMuted.Render(timeText))

	return strings.Join(parts, " | ")
}

// renderProgress renders progress information
func (s *StatusBarComponent) renderProgress() string {
	progress := s.status.Progress
	if progress == nil {
		return ""
	}

	// Calculate progress percentage
	percentage := float64(progress.Current) / float64(progress.Total) * 100
	if percentage > 100 {
		percentage = 100
	}

	// Create progress bar
	barWidth := 20
	filled := int(percentage / 100 * float64(barWidth))
	empty := barWidth - filled

	progressBar := strings.Repeat("█", filled) + strings.Repeat("░", empty)
	progressText := fmt.Sprintf("[%s] %d/%d", progressBar, progress.Current, progress.Total)

	if progress.Label != "" {
		progressText = fmt.Sprintf("%s %s", progress.Label, progressText)
	}

	return s.theme.Styles.TextPrimary.Render(progressText)
}

// renderSessionInfo renders session information
func (s *StatusBarComponent) renderSessionInfo() string {
	if s.status.SessionInfo != nil {
		info := s.status.SessionInfo
		var parts []string

		if info.MessageCount > 0 {
			parts = append(parts, fmt.Sprintf("%d msgs", info.MessageCount))
		}

		if info.TokenUsage.TotalTokens > 0 {
			parts = append(parts, utils.FormatTokenCount(info.TokenUsage.TotalTokens))
		}

		if info.Duration > 0 {
			parts = append(parts, utils.FormatDuration(info.Duration))
		}

		if len(parts) > 0 {
			return s.theme.Styles.TextSecondary.Render(strings.Join(parts, " | "))
		}
	}

	// Default session info from session object
	if s.session != nil {
		var parts []string

		msgCount := len(s.session.Messages)
		if msgCount > 0 {
			parts = append(parts, fmt.Sprintf("%d msgs", msgCount))
		}

		if s.session.TokenUsage.TotalTokens > 0 {
			parts = append(parts, utils.FormatTokenCount(s.session.TokenUsage.TotalTokens))
		}

		if len(parts) > 0 {
			return s.theme.Styles.TextSecondary.Render(strings.Join(parts, " | "))
		}
	}

	return ""
}

// renderConnectionStatus renders connection status
func (s *StatusBarComponent) renderConnectionStatus() string {
	conn := s.status.ConnectionInfo
	if conn == nil {
		return ""
	}

	var style lipgloss.Style
	var icon string

	if conn.Connected {
		style = s.theme.Styles.SuccessMessage
		icon = "🟢"
	} else {
		style = s.theme.Styles.ErrorMessage
		icon = "🔴"
	}

	connText := fmt.Sprintf("%s %s/%s", icon, conn.Provider, conn.Model)
	return style.Render(connText)
}

// renderSystemInfo renders system information
func (s *StatusBarComponent) renderSystemInfo() string {
	sys := s.status.SystemInfo
	if sys == nil {
		return ""
	}

	var parts []string

	if sys.CPUUsage > 0 {
		parts = append(parts, fmt.Sprintf("CPU: %.1f%%", sys.CPUUsage))
	}

	if sys.MemoryUsage > 0 {
		parts = append(parts, fmt.Sprintf("MEM: %.1f%%", sys.MemoryUsage))
	}

	if len(parts) > 0 {
		return s.theme.Styles.TextMuted.Render(strings.Join(parts, " "))
	}

	return ""
}

// RenderMinimal renders a minimal status bar with just essential information
func (s *StatusBarComponent) RenderMinimal() string {
	if s.width <= 0 {
		return ""
	}

	var parts []string

	// Mode
	if s.status.Mode != "" {
		parts = append(parts, s.theme.Styles.TextPrimary.Render(s.status.Mode))
	}

	// Message
	if s.status.Message != "" {
		parts = append(parts, s.theme.Styles.TextSecondary.Render(s.status.Message))
	}

	// Provider/Model
	if s.config != nil {
		providerText := fmt.Sprintf("%s/%s", s.config.LLM.Provider, s.config.LLM.Model)
		parts = append(parts, s.theme.Styles.TextMuted.Render(providerText))
	}

	content := strings.Join(parts, " | ")

	// Truncate if too long
	if lipgloss.Width(content) > s.width-2 {
		content = utils.TruncateString(content, s.width-2)
	}

	return s.theme.Styles.StatusContainer.Width(s.width).Render(content)
}

// RenderWithCustomContent renders the status bar with custom content
func (s *StatusBarComponent) RenderWithCustomContent(leftContent, centerContent, rightContent string) string {
	if s.width <= 0 {
		return ""
	}

	// Calculate available space
	leftWidth := lipgloss.Width(leftContent)
	rightWidth := lipgloss.Width(rightContent)
	centerWidth := s.width - leftWidth - rightWidth - 4 // 4 for spacing

	if centerWidth < 0 {
		centerWidth = 0
	}

	// Truncate center content if needed
	if lipgloss.Width(centerContent) > centerWidth {
		centerContent = utils.TruncateString(centerContent, centerWidth)
	}

	// Combine sections
	content := leftContent + strings.Repeat(" ", 2) + 
		utils.PadStringCenter(centerContent, centerWidth, ' ') + 
		strings.Repeat(" ", 2) + rightContent

	return s.theme.Styles.StatusContainer.Width(s.width).Render(content)
}

// GetHeight returns the height of the status bar component
func (s *StatusBarComponent) GetHeight() int {
	return 1
}

// SetConnectionStatus sets the connection status
func (s *StatusBarComponent) SetConnectionStatus(provider, model string, connected bool) {
	s.status.ConnectionInfo = &ConnectionInfo{
		Provider:  provider,
		Model:     model,
		Connected: connected,
		LastPing:  time.Now(),
	}
}

// SetSessionInfo sets the session information
func (s *StatusBarComponent) SetSessionInfo(messageCount int, tokenUsage types.TokenUsage, duration time.Duration) {
	s.status.SessionInfo = &SessionInfo{
		MessageCount: messageCount,
		TokenUsage:   tokenUsage,
		Duration:     duration,
	}
}

// SetSystemInfo sets the system information
func (s *StatusBarComponent) SetSystemInfo(cpuUsage, memoryUsage, diskUsage float64) {
	s.status.SystemInfo = &SystemInfo{
		CPUUsage:    cpuUsage,
		MemoryUsage: memoryUsage,
		DiskUsage:   diskUsage,
	}
}

// Clear clears all status information
func (s *StatusBarComponent) Clear() {
	s.status = StatusInfo{}
}
