# Arien-AI CLI Terminal System - Architecture & Implementation Plan

## Project Overview
A modern, powerful CLI terminal system built in Go 1.24.4 that integrates with LLM providers (Deepseek and Ollama) to execute shell commands intelligently and complete user tasks through function calling.

## Core Architecture

### 1. System Components

#### 1.1 Core Engine
- **Command Executor**: Executes shell commands with output capture
- **LLM Integration**: Handles communication with Deepseek and Ollama APIs
- **Function Calling System**: Manages tool definitions and execution
- **State Manager**: Maintains session state and context
- **Error Handler**: Comprehensive error handling with retry logic

#### 1.2 UI/UX Components (Bubble Tea Framework)
- **Onboarding Component**: Initial setup for LLM provider and API keys
- **Main Terminal Component**: Primary interface container
- **Header Component**: Shows session info, working directory, provider/model
- **Chat Input Component**: Message input with autocomplete
- **Message History Component**: Scrollable conversation history
- **Thinking Animation Component**: Visual feedback during processing
- **Slash Commands Component**: Multi-step popup for commands (/model, /provider, etc.)
- **Status Bar Component**: Real-time status updates

#### 1.3 Data Layer
- **Configuration Manager**: Stores user preferences and API keys
- **Session Storage**: Maintains conversation history and context
- **Cache System**: Caches frequently used data and responses

### 2. Technology Stack

#### 2.1 Core Dependencies
```go
// UI Framework
github.com/charmbracelet/bubbletea v1.3.5
github.com/charmbracelet/lipgloss v1.0.0
github.com/charmbracelet/bubbles v0.20.0

// HTTP Client & API Integration
github.com/sashabaranov/go-openai v1.35.6
net/http (standard library)

// CLI Framework
github.com/spf13/cobra v1.8.1
github.com/spf13/viper v1.19.0

// Utilities
github.com/fatih/color v1.18.0
github.com/briandowns/spinner v1.23.1
gopkg.in/yaml.v3 v3.0.1
```

#### 2.2 LLM Provider Integration
- **Deepseek API**: Models `deepseek-chat` and `deepseek-reasoner`
- **Ollama API**: Local model execution via REST API
- **Function Calling**: Tool definitions with detailed usage guidelines

### 3. Directory Structure

```
arien-ai/
├── cmd/
│   ├── root.go              # Root command and CLI setup
│   ├── interactive.go       # Interactive mode command
│   └── onboarding.go        # Initial setup command
├── internal/
│   ├── config/
│   │   ├── config.go        # Configuration management
│   │   └── providers.go     # LLM provider configurations
│   ├── llm/
│   │   ├── client.go        # LLM client interface
│   │   ├── deepseek.go      # Deepseek API implementation
│   │   ├── ollama.go        # Ollama API implementation
│   │   └── tools.go         # Function calling tools
│   ├── executor/
│   │   ├── shell.go         # Shell command execution
│   │   ├── output.go        # Output processing and formatting
│   │   └── safety.go        # Command safety checks
│   ├── ui/
│   │   ├── models/
│   │   │   ├── main.go      # Main terminal model
│   │   │   ├── onboarding.go # Onboarding model
│   │   │   ├── chat.go      # Chat interface model
│   │   │   └── commands.go  # Slash commands model
│   │   ├── components/
│   │   │   ├── header.go    # Header component
│   │   │   ├── input.go     # Input component
│   │   │   ├── history.go   # Message history component
│   │   │   ├── thinking.go  # Thinking animation
│   │   │   └── statusbar.go # Status bar component
│   │   └── styles/
│   │       └── theme.go     # UI styling and themes
│   ├── session/
│   │   ├── manager.go       # Session management
│   │   ├── storage.go       # Persistent storage
│   │   └── context.go       # Context management
│   └── utils/
│       ├── retry.go         # Retry logic implementation
│       ├── validation.go    # Input validation
│       └── formatting.go    # Output formatting utilities
├── pkg/
│   └── types/
│       ├── message.go       # Message types
│       ├── session.go       # Session types
│       └── config.go        # Configuration types
├── scripts/
│   └── install.sh           # Universal installation script
├── docs/
│   ├── USAGE.md            # Usage documentation
│   ├── TOOLS.md            # Function calling tools documentation
│   └── EXAMPLES.md         # Usage examples
├── go.mod
├── go.sum
├── main.go                 # Application entry point
├── README.md
└── ARCHITECTURE.md         # This file
```

### 4. Function Calling System

#### 4.1 Shell Tool Definition
```json
{
  "name": "execute_shell_command",
  "description": "Execute shell commands with safety checks and output capture",
  "parameters": {
    "command": "string - The shell command to execute",
    "working_directory": "string - Directory to execute command in",
    "timeout": "int - Command timeout in seconds (default: 30)",
    "capture_output": "bool - Whether to capture command output",
    "require_confirmation": "bool - Whether to ask user confirmation"
  },
  "usage_guidelines": {
    "when_to_use": [
      "File system operations",
      "Process management", 
      "System information gathering",
      "Development tasks (build, test, deploy)"
    ],
    "when_not_to_use": [
      "Destructive operations without confirmation",
      "Commands requiring interactive input",
      "Long-running background processes"
    ],
    "parallel_execution": "Use for independent operations",
    "sequential_execution": "Use when commands depend on previous results"
  }
}
```

### 5. System Prompt Framework

#### 5.1 Core Capabilities
- **Command Execution**: Safe shell command execution with output analysis
- **File Operations**: Read, write, modify files with proper permissions
- **Process Management**: Start, stop, monitor system processes
- **Development Tasks**: Build, test, deploy applications
- **System Analysis**: Gather system information and diagnostics

#### 5.2 Safety Guidelines
- Always confirm destructive operations
- Validate command syntax before execution
- Check file permissions and ownership
- Monitor resource usage during execution
- Implement timeout mechanisms for long-running commands

### 6. Implementation Phases

#### Phase 1: Core Infrastructure (Week 1)
- [x] Project setup and dependency management
- [x] Basic CLI structure with Cobra
- [x] Configuration management system
- [x] LLM client interfaces

#### Phase 2: UI Framework (Week 2)
- [x] Bubble Tea application structure
- [x] Core UI components
- [x] Onboarding flow
- [ ] Basic chat interface (needs completion)

#### Phase 3: LLM Integration (Week 3)
- [x] Deepseek API integration
- [x] Ollama API integration
- [x] Function calling implementation
- [x] Tool definitions and validation

#### Phase 4: Command Execution (Week 4)
- [x] Shell command executor
- [x] Output processing and formatting
- [x] Safety checks and validation
- [x] Error handling and retry logic

#### Phase 5: Advanced Features (Week 5)
- [x] Session management
- [ ] Message history persistence (needs completion)
- [x] Slash commands system
- [x] Advanced UI components

#### Phase 6: Testing & Polish (Week 6)
- [ ] Comprehensive testing
- [ ] Documentation completion
- [x] Installation scripts
- [ ] Performance optimization

### 7. Installation & Distribution

#### 7.1 Universal Installation Script
- Cross-platform support (Windows 11 WSL, macOS, Linux)
- Automatic dependency detection and installation
- Global binary installation
- Update and uninstall capabilities
- Configuration migration support

#### 7.2 Build & Release Process
- GitHub Actions for automated builds
- Cross-compilation for multiple platforms
- Binary distribution via GitHub Releases
- Package manager integration (Homebrew, Chocolatey, APT)

### 8. Security Considerations

#### 8.1 Command Safety
- Whitelist/blacklist for dangerous commands
- User confirmation for destructive operations
- Sandboxing for untrusted commands
- Resource limits and timeouts

#### 8.2 API Security
- Secure API key storage
- Rate limiting and quota management
- Request/response validation
- Error message sanitization

### 9. Performance Optimization

#### 9.1 Response Time
- Async command execution
- Streaming responses from LLM
- Efficient UI updates
- Smart caching strategies

#### 9.2 Resource Management
- Memory usage optimization
- CPU usage monitoring
- Network request batching
- Garbage collection tuning

This architecture provides a solid foundation for building a powerful, safe, and user-friendly CLI terminal system that leverages LLM capabilities for intelligent command execution and task completion.
