# Arien-AI Usage Examples

This document provides comprehensive examples of using Arien-AI for various tasks.

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [Development Tasks](#development-tasks)
3. [System Administration](#system-administration)
4. [File Operations](#file-operations)
5. [Git Operations](#git-operations)
6. [Advanced Workflows](#advanced-workflows)
7. [Function Calling Examples](#function-calling-examples)

## Basic Usage

### Starting Arien-AI

```bash
# First-time setup
arien-ai onboard

# Start interactive mode
arien-ai interactive

# Start with specific provider/model
arien-ai interactive --provider deepseek --model deepseek-chat

# Start in safe mode
arien-ai interactive --safe-mode
```

### Basic Conversations

```
User: Hello! Can you help me understand my system?
