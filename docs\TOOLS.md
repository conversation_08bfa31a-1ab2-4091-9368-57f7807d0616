# Function Calling Tools Documentation

This document provides comprehensive information about the function calling tools available in Arien-AI.

## Overview

Arien-AI supports advanced function calling that allows the AI to execute shell commands, read/write files, and gather system information. Each tool has detailed usage guidelines, safety checks, and examples.

## Available Tools

### 1. Shell Command Execution

**Function Name:** `execute_shell_command`

**Description:** Execute shell commands with comprehensive safety checks and output capture.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `command` | string | Yes | The shell command to execute |
| `working_directory` | string | No | Directory to execute command in (default: current) |
| `timeout` | integer | No | Command timeout in seconds (default: 30, max: 300) |
| `capture_output` | boolean | No | Whether to capture command output (default: true) |
| `require_confirmation` | boolean | No | Whether to ask user confirmation (default: false) |
| `environment` | object | No | Additional environment variables |

#### Usage Guidelines

**When to Use:**
- File system operations (`ls`, `find`, `grep`)
- Git operations (`git status`, `git log`, `git diff`)
- Build operations (`npm install`, `go build`, `make`)
- System information (`ps`, `top`, `df`, `uname`)
- Development tasks (running tests, starting servers)

**When NOT to Use:**
- Destructive operations without confirmation (`rm -rf`, `format`)
- Commands requiring interactive input (editors, prompts)
- Long-running background processes without proper timeout
- System configuration changes without user consent

**Parallel vs Sequential Execution:**

**Parallel Execution** - Use for independent operations:
```json
[
  {"command": "ls /home", "working_directory": "/"},
  {"command": "df -h", "working_directory": "/"},
  {"command": "free -m", "working_directory": "/"}
]
```

**Sequential Execution** - Use for dependent operations:
```json
[
  {"command": "cd /project", "working_directory": "/"},
  {"command": "npm install", "working_directory": "/project"},
  {"command": "npm test", "working_directory": "/project"}
]
```

#### Examples

**Basic file listing:**
```json
{
  "command": "ls -la",
  "working_directory": "/home/<USER>",
  "timeout": 10
}
```

**Git status check:**
```json
{
  "command": "git status --porcelain",
  "working_directory": "/project",
  "capture_output": true
}
```

**Build with confirmation:**
```json
{
  "command": "rm -rf build && npm run build",
  "working_directory": "/project",
  "require_confirmation": true,
  "timeout": 120
}
```

**Environment-specific command:**
```json
{
  "command": "echo $CUSTOM_VAR",
  "environment": {
    "CUSTOM_VAR": "hello world"
  }
}
```

#### Safety Features

- **Command Validation**: Blocks dangerous patterns
- **Working Directory Validation**: Ensures directory exists and is accessible
- **Timeout Protection**: Prevents runaway processes
- **Output Size Limits**: Prevents memory exhaustion
- **Permission Checks**: Validates file/directory permissions

---

### 2. File Reading

**Function Name:** `read_file`

**Description:** Read file contents with safety checks and encoding support.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file_path` | string | Yes | Path to the file to read |
| `encoding` | string | No | File encoding (default: utf-8) |
| `max_size` | integer | No | Maximum file size in bytes (default: 1MB, max: 10MB) |
| `line_range` | array | No | Range of lines to read [start, end] (1-indexed) |

#### Usage Guidelines

**When to Use:**
- Reading configuration files for analysis
- Examining log files for debugging
- Reading source code for understanding
- Checking file contents before modification

**When NOT to Use:**
- Reading binary files (images, executables)
- Reading extremely large files without size limits
- Reading files requiring special permissions

#### Examples

**Read configuration file:**
```json
{
  "file_path": "config.yaml",
  "max_size": 10240
}
```

**Read specific lines from log:**
```json
{
  "file_path": "/var/log/app.log",
  "line_range": [100, 200],
  "max_size": 1048576
}
```

**Read with specific encoding:**
```json
{
  "file_path": "data.txt",
  "encoding": "latin-1",
  "max_size": 5242880
}
```

---

### 3. File Writing

**Function Name:** `write_file`

**Description:** Write content to files with backup options and safety checks.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file_path` | string | Yes | Path to the file to write |
| `content` | string | Yes | Content to write to the file |
| `mode` | string | No | Write mode: 'write' or 'append' (default: write) |
| `encoding` | string | No | File encoding (default: utf-8) |
| `backup` | boolean | No | Create backup of existing file (default: true) |
| `permissions` | string | No | File permissions in octal (default: 644) |

#### Usage Guidelines

**When to Use:**
- Creating new configuration files
- Writing generated code or scripts
- Saving processed data or reports
- Creating documentation files

**When NOT to Use:**
- Writing to system files without proper permissions
- Overwriting critical files without backups
- Writing binary content (use appropriate tools)

#### Examples

**Create new configuration:**
```json
{
  "file_path": "config.yaml",
  "content": "server:\n  port: 8080\n  host: localhost",
  "backup": false
}
```

**Update existing file with backup:**
```json
{
  "file_path": "app.conf",
  "content": "debug=true\nlog_level=info",
  "backup": true,
  "mode": "write"
}
```

**Append to log file:**
```json
{
  "file_path": "app.log",
  "content": "2024-01-01 12:00:00 INFO Application started\n",
  "mode": "append",
  "backup": false
}
```

---

### 4. System Information

**Function Name:** `get_system_info`

**Description:** Retrieve system information including OS, hardware, and environment details.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `info_type` | string | No | Type of information: 'basic', 'hardware', 'environment', 'network', 'processes', 'all' |
| `include_sensitive` | boolean | No | Include potentially sensitive information (default: false) |

#### Information Types

**basic**: Operating system, architecture, hostname
**hardware**: CPU, memory, disk space
**environment**: Environment variables, PATH
**network**: Network interfaces, connectivity
**processes**: Running processes and services
**all**: Complete system information

#### Examples

**Get basic system info:**
```json
{
  "info_type": "basic"
}
```

**Check hardware resources:**
```json
{
  "info_type": "hardware"
}
```

**Debug environment:**
```json
{
  "info_type": "environment",
  "include_sensitive": false
}
```

**Complete system overview:**
```json
{
  "info_type": "all",
  "include_sensitive": true
}
```

## Best Practices

### 1. Error Handling

Always check tool execution results:
- Monitor exit codes for shell commands
- Validate file operations success
- Handle timeout scenarios gracefully

### 2. Security Considerations

- Use `require_confirmation` for destructive operations
- Set appropriate timeouts for long-running commands
- Validate file paths to prevent traversal attacks
- Use minimal permissions for file operations

### 3. Performance Optimization

- Use appropriate timeouts to prevent hanging
- Limit file sizes when reading large files
- Use line ranges for large log files
- Batch independent operations for parallel execution

### 4. User Experience

- Provide clear feedback for long operations
- Use confirmation prompts for risky actions
- Display progress for time-consuming tasks
- Format output for readability

## Tool Validation

All tool calls are validated before execution:

1. **Parameter Validation**: Required parameters, type checking
2. **Security Validation**: Command safety, path validation
3. **Resource Validation**: Size limits, timeout bounds
4. **Permission Validation**: File access, directory permissions

## Error Codes

Common error scenarios and their handling:

- **Invalid Parameters**: Missing required parameters, wrong types
- **Security Violations**: Blocked commands, unsafe paths
- **Resource Limits**: File too large, timeout exceeded
- **Permission Denied**: Insufficient file/directory permissions
- **Execution Failures**: Command not found, syntax errors

## Advanced Usage

### Chaining Operations

Combine multiple tools for complex workflows:

1. Get system info to understand environment
2. Read configuration files to understand setup
3. Execute commands based on configuration
4. Write results or logs as needed

### Conditional Execution

Use tool results to make decisions:

1. Check if file exists before reading
2. Validate command success before proceeding
3. Create backups before destructive operations
4. Monitor resource usage before intensive tasks

### Error Recovery

Implement robust error handling:

1. Retry transient failures
2. Fallback to alternative approaches
3. Clean up partial operations
4. Provide meaningful error messages
